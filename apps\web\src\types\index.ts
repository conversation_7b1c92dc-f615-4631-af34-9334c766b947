// Core types for the VirtualRealTour platform

export interface Tenant {
  id: string;
  name: string;
  subdomain: string;
  domain?: string;
  config: TenantConfig;
  status: 'active' | 'inactive' | 'suspended';
  created_at: string;
  updated_at: string;
}

export interface TenantConfig {
  theme: string;
  features: string[];
  branding: {
    primary_color: string;
    secondary_color: string;
    logo_url?: string;
  };
}

export interface Store {
  id: string;
  tenant_id: string;
  name: string;
  description?: string;
  slug: string;
  logo_url?: string;
  banner_url?: string;
  status: 'active' | 'inactive';
  communication_config: StoreCommunicationConfig;
  created_at: string;
  updated_at: string;
  // Computed fields
  _count?: {
    tours: number;
    products: number;
    productInquiries?: number;
  };
}

export interface StoreCommunicationConfig {
  whatsapp_enabled: boolean;
  whatsapp_number?: string;
  external_website_enabled: boolean;
  external_website_url?: string;
  track_referrals: boolean;
}

export interface Product {
  id: string;
  store_id: string;
  tenant_id: string;
  name: string;
  description?: string;
  price: number;
  currency: string;
  images: string[];
  has_3d_model: boolean;
  vr_ready: boolean;
  video_360_url?: string;
  external_url?: string;
  whatsapp_inquiry_enabled: boolean;
  referral_tracking_enabled: boolean;
  metadata: ProductMetadata;
  status: 'active' | 'inactive' | 'draft';
  created_at: string;
  updated_at: string;
  // Relations
  store?: Store;
}

export interface ProductMetadata {
  category: string;
  tags?: string[];
  specifications?: Record<string, any>;
  [key: string]: any;
}

export interface Tour {
  id: string;
  tenant_id: string;
  store_id: string;
  title: string;
  description?: string;
  tour_type: 'panorama' | '3d' | 'video' | 'embed';
  tour_url: string;
  hotspots: TourHotspot[];
  metadata: TourMetadata;
  status: 'active' | 'inactive' | 'draft';
  created_at: string;
  updated_at: string;
  // Relations
  store?: Store;
}

export interface TourHotspot {
  id: string;
  position: {
    x: number;
    y: number;
    z?: number;
  };
  type: 'product' | 'info' | 'whatsapp' | 'external' | 'demo';
  title: string;
  description: string;
  action_data?: Record<string, any>;
}

export interface TourMetadata {
  duration?: string;
  featured?: boolean;
  difficulty?: 'easy' | 'medium' | 'advanced';
  [key: string]: any;
}

export interface ProductInquiry {
  id: string;
  product_id: string;
  store_id: string;
  tenant_id: string;
  customer_name: string;
  customer_email: string;
  customer_phone?: string;
  inquiry_type: 'whatsapp' | 'external_referral';
  message: string;
  status: 'sent' | 'responded' | 'converted' | 'closed';
  whatsapp_message_id?: string;
  referral_url?: string;
  created_at: string;
  updated_at: string;
  // Relations
  product?: Product;
}

export interface ReferralAnalytics {
  id: string;
  product_id: string;
  store_id: string;
  tenant_id: string;
  referral_url: string;
  clicks: number;
  conversions: number;
  revenue_tracked?: number;
  utm_source: string;
  utm_medium: string;
  utm_campaign: string;
  created_at: string;
  updated_at: string;
  // Relations
  product?: Product;
}

export interface User {
  id: string;
  tenant_id: string;
  email: string;
  name: string;
  role: 'super_admin' | 'tenant_admin' | 'store_owner' | 'customer';
  status: 'active' | 'inactive';
  created_at: string;
  updated_at: string;
}

// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface PaginatedResponse<T = any> {
  success: boolean;
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  message?: string;
  error?: string;
}

// Form types
export interface CreateStoreRequest {
  name: string;
  description?: string;
  slug: string;
  logo_url?: string;
  banner_url?: string;
  communication_config: StoreCommunicationConfig;
}

export interface UpdateStoreRequest extends Partial<CreateStoreRequest> {
  status?: 'active' | 'inactive';
}

export interface CreateProductRequest {
  store_id: string;
  name: string;
  description?: string;
  price: number;
  currency?: string;
  images: string[];
  has_3d_model?: boolean;
  vr_ready?: boolean;
  video_360_url?: string;
  external_url?: string;
  whatsapp_inquiry_enabled?: boolean;
  referral_tracking_enabled?: boolean;
  metadata?: ProductMetadata;
}

export interface UpdateProductRequest extends Partial<CreateProductRequest> {
  status?: 'active' | 'inactive' | 'draft';
}

export interface CreateTourRequest {
  store_id: string;
  title: string;
  description?: string;
  tour_type: 'panorama' | '3d' | 'video' | 'embed';
  tour_url: string;
  hotspots?: TourHotspot[];
  metadata?: TourMetadata;
}

export interface UpdateTourRequest extends Partial<CreateTourRequest> {
  status?: 'active' | 'inactive' | 'draft';
}

export interface CreateInquiryRequest {
  product_id: string;
  customer_name: string;
  customer_email: string;
  customer_phone?: string;
  inquiry_type: 'whatsapp' | 'external_referral';
  message: string;
}

// Search and filter types
export interface SearchFilters {
  query?: string;
  category?: string;
  price_min?: number;
  price_max?: number;
  has_3d_model?: boolean;
  vr_ready?: boolean;
  store_id?: string;
  status?: string;
}

export interface PaginationParams {
  page?: number;
  limit?: number;
}

// Dashboard analytics types
export interface DashboardStats {
  total_stores: number;
  total_products: number;
  total_tours: number;
  total_inquiries: number;
  total_clicks: number;
  total_conversions: number;
  total_revenue: number;
  conversion_rate: number;
}

export interface AnalyticsTimeframe {
  period: '7d' | '30d' | '90d' | '1y';
  start_date?: string;
  end_date?: string;
}

// Map and location types
export interface StoreLocation {
  store_id: string;
  name: string;
  latitude: number;
  longitude: number;
  address?: string;
  tour_count: number;
  featured_tour?: Tour;
}

// WhatsApp integration types
export interface WhatsAppInquiryRequest {
  product_id: string;
  customer_name: string;
  customer_phone: string;
  message: string;
}

export interface WhatsAppResponse {
  success: boolean;
  message_id?: string;
  error?: string;
}
