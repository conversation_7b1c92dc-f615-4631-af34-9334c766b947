'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  PlayIcon,
  EyeIcon,
  MapPinIcon,
  ChatBubbleLeftRightIcon,
  ArrowRightIcon,
  SparklesIcon
} from '@heroicons/react/24/outline';
// import { Header } from '@/components/layout/Header';
// import { Footer } from '@/components/layout/Footer';
// import { TourCard } from '@/components/tours/TourCard';
// import { StoreMap } from '@/components/map/StoreMap';
// import { FeaturedStores } from '@/components/stores/FeaturedStores';
import { useQuery } from 'react-query';
import apiClient from '@/lib/api';
import Link from 'next/link';

export default function HomePage() {
  const [activeSection, setActiveSection] = useState('hero');

  // Fetch featured tours and stores
  const { data: toursData } = useQuery(
    'featured-tours',
    () => apiClient.getTours(undefined, { page: 1, limit: 6 }),
    { enabled: true }
  );

  const { data: storesData } = useQuery(
    'featured-stores',
    () => apiClient.getStores({ page: 1, limit: 8 }),
    { enabled: true }
  );

  useEffect(() => {
    const handleScroll = () => {
      const sections = ['hero', 'features', 'tours', 'stores', 'map'];
      const scrollPosition = window.scrollY + 100;

      for (const section of sections) {
        const element = document.getElementById(section);
        if (element) {
          const { offsetTop, offsetHeight } = element;
          if (scrollPosition >= offsetTop && scrollPosition < offsetTop + offsetHeight) {
            setActiveSection(section);
            break;
          }
        }
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <div className="min-h-screen bg-white">
      {/* <Header /> */}

      {/* Hero Section */}
      <section id="hero" className="relative min-h-screen flex items-center justify-center overflow-hidden">
        {/* Background Video/Image */}
        <div className="absolute inset-0 z-0">
          <div className="absolute inset-0 bg-black/40 z-10" />
          <video
            autoPlay
            muted
            loop
            playsInline
            className="w-full h-full object-cover"
            poster="https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=1920&q=80"
          >
            <source src="/videos/hero-tour.mp4" type="video/mp4" />
          </video>
        </div>

        {/* Hero Content */}
        <div className="relative z-20 text-center text-white px-4 sm:px-6 lg:px-8 max-w-5xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <h1 className="text-4xl sm:text-6xl lg:text-7xl font-bold mb-6 leading-tight">
              Experience Shopping
              <br />
              <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                Like Never Before
              </span>
            </h1>

            <p className="text-xl sm:text-2xl mb-8 text-gray-200 max-w-3xl mx-auto leading-relaxed">
              Immerse yourself in virtual tours, explore products in 360°, and connect directly with stores through WhatsApp. The future of shopping is here.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Link href="/explore" className="btn-primary text-lg px-8 py-4 group">
                Start Exploring
                <ArrowRightIcon className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
              </Link>

              <button className="btn-secondary text-lg px-8 py-4 bg-white/10 border-white/20 text-white hover:bg-white/20 group">
                <PlayIcon className="w-5 h-5 mr-2" />
                Watch Demo
              </button>
            </div>
          </motion.div>

          {/* Scroll Indicator */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1.5, duration: 0.5 }}
            className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
          >
            <div className="w-6 h-10 border-2 border-white/50 rounded-full flex justify-center">
              <div className="w-1 h-3 bg-white/70 rounded-full mt-2 animate-bounce" />
            </div>
          </motion.div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
              Revolutionizing Product Discovery
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Our platform combines cutting-edge virtual reality technology with seamless communication tools to create the ultimate shopping experience.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                icon: EyeIcon,
                title: '360° Virtual Tours',
                description: 'Immersive experiences that let you explore every detail of products and stores.',
                color: 'bg-blue-500'
              },
              {
                icon: SparklesIcon,
                title: '3D Product Models',
                description: 'Interactive 3D models that you can rotate, zoom, and examine from every angle.',
                color: 'bg-purple-500'
              },
              {
                icon: ChatBubbleLeftRightIcon,
                title: 'WhatsApp Integration',
                description: 'Connect instantly with store owners and get personalized assistance.',
                color: 'bg-green-500'
              },
              {
                icon: MapPinIcon,
                title: 'Store Discovery',
                description: 'Find amazing stores and products near you with our interactive map.',
                color: 'bg-red-500'
              }
            ].map((feature, index) => (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="text-center group"
              >
                <div className={`${feature.color} w-16 h-16 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-200`}>
                  <feature.icon className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">{feature.title}</h3>
                <p className="text-gray-600 leading-relaxed">{feature.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Featured Tours Section */}
      <section id="tours" className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
              Featured Virtual Tours
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Step into immersive experiences and discover products like never before.
            </p>
          </motion.div>

          {toursData?.data && toursData.data.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {toursData.data.slice(0, 6).map((tour, index) => (
                <motion.div
                  key={tour.id}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="card-hover p-6"
                >
                  <h3 className="text-xl font-semibold mb-2">{tour.title}</h3>
                  <p className="text-gray-600 mb-4">{tour.description}</p>
                  <div className="flex items-center justify-between">
                    <span className="badge badge-info">{tour.tour_type}</span>
                    <Link href={`/tours/${tour.id}`} className="btn-primary">
                      View Tour
                    </Link>
                  </div>
                </motion.div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <p className="text-gray-500">Loading featured tours...</p>
            </div>
          )}

          <motion.div
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            viewport={{ once: true }}
            className="text-center mt-12"
          >
            <Link href="/tours" className="btn-primary text-lg px-8 py-4">
              View All Tours
            </Link>
          </motion.div>
        </div>
      </section>

      {/* Featured Stores Section */}
      <section id="stores" className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
              Discover Amazing Stores
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Explore curated collections from the world's most innovative retailers.
            </p>
          </motion.div>

          {storesData?.data && (
            <FeaturedStores stores={storesData.data} />
          )}
        </div>
      </section>

      {/* Interactive Map Section */}
      <section id="map" className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
              Explore Stores Near You
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Find virtual tours and immersive experiences in your area.
            </p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="h-96 rounded-2xl overflow-hidden shadow-xl"
          >
            <StoreMap stores={storesData?.data || []} />
          </motion.div>
        </div>
      </section>

      <Footer />
    </div>
  );
}
