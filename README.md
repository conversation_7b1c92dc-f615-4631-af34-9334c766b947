# VirtualRealTour - Product Discovery & Referral Platform

A white-label virtual product discovery and referral platform that connects customers with store owners through immersive virtual tours, WhatsApp integration, and external website referrals.

## 🏗️ Architecture

This is a **Turborepo monorepo** with the following structure:

```
virtualrealtour/
├── apps/
│   └── api/              # Express.js backend API
├── packages/
│   ├── types/            # Shared TypeScript definitions
│   └── database/         # Prisma database package
├── .ai/                  # AI assistant documentation
└── tools/                # Build tools & utilities
```

## 🚀 Quick Start

### Prerequisites

- Node.js 18+ 
- PostgreSQL database
- npm or yarn

### Installation

1. **Clone and install dependencies:**
   ```bash
   git clone <repository-url>
   cd virtualrealtour
   npm install
   ```

2. **Set up environment variables:**
   ```bash
   cd apps/api
   cp .env.example .env
   # Edit .env with your database URL and other configurations
   ```

3. **Set up the database:**
   ```bash
   # Generate Prisma client
   npm run db:generate
   
   # Run database migrations
   npm run db:migrate
   
   # Seed with demo data
   npm run db:seed
   ```

4. **Start the development server:**
   ```bash
   npm run api:dev
   ```

The API will be available at `http://localhost:3001`

## 🌐 API Endpoints

### Core Endpoints

- **Health Check:** `GET /api/health`
- **API Info:** `GET /api`

### Stores

- `GET /api/stores` - List all stores
- `GET /api/stores/:storeId` - Get store details
- `GET /api/stores/:storeId/products` - Get store products
- `GET /api/stores/:storeId/config` - Get store configuration (auth required)
- `PUT /api/stores/:storeId/config` - Update store configuration (auth required)
- `GET /api/stores/:storeId/inquiries` - Get store inquiries (auth required)
- `GET /api/stores/:storeId/analytics` - Get store analytics (auth required)

### Products

- `GET /api/products/search?q=term` - Search products
- `GET /api/products/:productId` - Get product details
- `POST /api/products/:productId/whatsapp-inquiry` - Send WhatsApp inquiry
- `POST /api/products/:productId/external-referral` - Generate referral URL
- `GET /api/products/:productId/track-click` - Track referral click
- `GET /api/products/:productId/analytics` - Get product analytics

## 🏪 Multi-Tenant Architecture

The platform supports multi-tenancy through subdomain/domain detection:

- **Subdomain:** `demo.virtualrealtour.com` → tenant: "demo"
- **Custom Domain:** `store.example.com` → tenant with domain: "store.example.com"

Each tenant has isolated data and configuration.

## 📱 WhatsApp Integration

The platform integrates with WhatsApp Business API to enable direct customer-to-store communication:

```typescript
// Example WhatsApp inquiry
POST /api/products/:productId/whatsapp-inquiry
{
  "customer_name": "John Doe",
  "customer_email": "<EMAIL>",
  "customer_phone": "+1234567890",
  "message": "I'm interested in this product. Can you provide more details?"
}
```

## 🔗 External Referral Tracking

Generate tracked URLs that redirect customers to external store websites:

```typescript
// Example referral generation
POST /api/products/:productId/external-referral
{
  "customer_email": "<EMAIL>",
  "utm_source": "virtualrealtour",
  "utm_medium": "referral",
  "utm_campaign": "product_discovery"
}

// Response
{
  "success": true,
  "data": {
    "referralUrl": "https://store.com/product?utm_source=virtualrealtour&utm_medium=referral...",
    "trackingId": "uuid",
    "analytics": {
      "clicks": 0,
      "conversions": 0
    }
  }
}
```

## 🗄️ Database Schema

Key entities:

- **Tenant** - Multi-tenant configuration
- **Store** - Individual stores within a tenant
- **Product** - Products with referral/inquiry metadata
- **Tour** - Virtual tour configurations
- **ProductInquiry** - Customer inquiries (WhatsApp/referral)
- **ReferralAnalytics** - Tracking data for external referrals

## 🧪 Testing

```bash
# Run all tests
npm run test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test -- --coverage
```

## 🔧 Development

### Available Scripts

```bash
# Development
npm run dev              # Start all apps in development mode
npm run api:dev          # Start only the API server

# Building
npm run build            # Build all packages and apps
npm run type-check       # Type check all packages

# Database
npm run db:migrate       # Run database migrations
npm run db:seed          # Seed database with demo data
npm run db:generate      # Generate Prisma client

# Testing
npm run test             # Run tests
npm run lint             # Run linting
```

### Environment Variables

Key environment variables for the API:

```env
# Database
DATABASE_URL=postgresql://user:pass@localhost:5432/virtualrealtour

# Server
PORT=3001
NODE_ENV=development

# Authentication
JWT_SECRET=your-secret-key
JWT_EXPIRY=24h

# CORS
CORS_ORIGIN=http://localhost:3000

# WhatsApp Business API
WHATSAPP_BUSINESS_API_TOKEN=your-token
```

## 📊 Analytics & Tracking

The platform provides comprehensive analytics:

### Product Analytics
- Inquiry rates
- WhatsApp engagement
- Referral click-through rates
- Conversion tracking

### Store Analytics
- Total products and inquiries
- Referral performance
- Customer engagement metrics

### Tenant Analytics
- Cross-store performance
- Geographic insights
- Feature adoption rates

## 🔐 Security

- **Helmet.js** for security headers
- **Rate limiting** to prevent abuse
- **CORS** configuration
- **Input validation** with express-validator
- **Multi-tenant data isolation**

## 🚀 Deployment

The application is designed for easy deployment:

1. **Database:** PostgreSQL (hosted or self-managed)
2. **API:** Node.js server (Vercel, Railway, VPS)
3. **Environment:** Production environment variables
4. **Monitoring:** Built-in logging with Winston

## 📚 API Documentation

Visit `/api` when the server is running for interactive API documentation and endpoint details.

## 🤝 Contributing

1. Follow the established coding patterns in `.ai/` documentation
2. Write tests for new features
3. Update documentation as needed
4. Follow TypeScript best practices

## 📄 License

This project is part of the VirtualRealTour platform. See license terms in the main repository.
