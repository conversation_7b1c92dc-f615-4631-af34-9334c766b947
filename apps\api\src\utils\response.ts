import { Response } from 'express';
import type { ApiResponse, PaginatedResponse } from '@virtualrealtour/types';

export class ResponseHelper {
  static success<T>(res: Response, data: T, message?: string, statusCode: number = 200): Response {
    const response: ApiResponse<T> = {
      success: true,
      data,
      message,
    };
    return res.status(statusCode).json(response);
  }

  static error(res: Response, error: string, statusCode: number = 500): Response {
    const response: ApiResponse = {
      success: false,
      error,
    };
    return res.status(statusCode).json(response);
  }

  static paginated<T>(
    res: Response,
    data: T[],
    page: number,
    limit: number,
    total: number,
    message?: string
  ): Response {
    const totalPages = Math.ceil(total / limit);
    
    const response: ApiResponse<PaginatedResponse<T>> = {
      success: true,
      data: {
        data,
        pagination: {
          page,
          limit,
          total,
          totalPages,
        },
      },
      message,
    };
    
    return res.status(200).json(response);
  }

  static created<T>(res: Response, data: T, message?: string): Response {
    return this.success(res, data, message, 201);
  }

  static noContent(res: Response): Response {
    return res.status(204).send();
  }

  static badRequest(res: Response, error: string): Response {
    return this.error(res, error, 400);
  }

  static unauthorized(res: Response, error: string = 'Unauthorized'): Response {
    return this.error(res, error, 401);
  }

  static forbidden(res: Response, error: string = 'Forbidden'): Response {
    return this.error(res, error, 403);
  }

  static notFound(res: Response, error: string = 'Resource not found'): Response {
    return this.error(res, error, 404);
  }

  static conflict(res: Response, error: string = 'Resource already exists'): Response {
    return this.error(res, error, 409);
  }

  static internalError(res: Response, error: string = 'Internal server error'): Response {
    return this.error(res, error, 500);
  }
}

export default ResponseHelper;
