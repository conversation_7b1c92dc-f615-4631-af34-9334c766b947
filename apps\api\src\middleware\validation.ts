import { Request, Response, NextFunction } from 'express';
import { body, param, query, validationResult } from 'express-validator';
import { ValidationError } from '../utils/errors.js';

/**
 * Middleware to handle validation errors
 */
export function handleValidationErrors(req: Request, res: Response, next: NextFunction) {
  const errors = validationResult(req);

  if (!errors.isEmpty()) {
    const errorMessages = errors.array().map(error => ({
      field: error.type === 'field' ? error.path : 'unknown',
      message: error.msg,
      value: error.type === 'field' ? error.value : undefined,
    }));

    throw new ValidationError(`Validation failed: ${errorMessages.map(e => e.message).join(', ')}`);
  }

  next();
}

// Common validation rules
export const validateProductInquiry = [
  body('customer_name')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Customer name must be between 2 and 100 characters'),
  body('customer_email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Valid email address is required'),
  body('customer_phone')
    .optional()
    .isMobilePhone('any')
    .withMessage('Valid phone number is required'),
  body('message')
    .trim()
    .isLength({ min: 10, max: 1000 })
    .withMessage('Message must be between 10 and 1000 characters'),
  body('inquiry_type')
    .isIn(['whatsapp', 'external_referral'])
    .withMessage('Inquiry type must be whatsapp or external_referral'),
  handleValidationErrors,
];

export const validateWhatsAppInquiry = [
  body('customer_name')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Customer name must be between 2 and 100 characters'),
  body('customer_email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Valid email address is required'),
  body('customer_phone')
    .optional()
    .isMobilePhone('any')
    .withMessage('Valid phone number is required'),
  body('message')
    .trim()
    .isLength({ min: 10, max: 1000 })
    .withMessage('Message must be between 10 and 1000 characters'),
  handleValidationErrors,
];

export const validateExternalReferral = [
  body('customer_email')
    .optional()
    .isEmail()
    .normalizeEmail()
    .withMessage('Valid email address is required'),
  body('utm_source')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('UTM source must be less than 100 characters'),
  body('utm_medium')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('UTM medium must be less than 100 characters'),
  body('utm_campaign')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('UTM campaign must be less than 100 characters'),
  handleValidationErrors,
];

export const validateProductId = [
  param('productId')
    .isUUID()
    .withMessage('Valid product ID is required'),
  handleValidationErrors,
];

export const validateStoreId = [
  param('storeId')
    .isUUID()
    .withMessage('Valid store ID is required'),
  handleValidationErrors,
];

export const validatePagination = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  query('sortBy')
    .optional()
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('Sort field must be specified'),
  query('sortOrder')
    .optional()
    .isIn(['asc', 'desc'])
    .withMessage('Sort order must be asc or desc'),
  handleValidationErrors,
];

export const validateStoreConfig = [
  body('whatsapp_business_number')
    .optional()
    .isMobilePhone('any')
    .withMessage('Valid WhatsApp business number is required'),
  body('whatsapp_enabled')
    .optional()
    .isBoolean()
    .withMessage('WhatsApp enabled must be a boolean'),
  body('external_website_url')
    .optional()
    .isURL()
    .withMessage('Valid external website URL is required'),
  body('external_website_enabled')
    .optional()
    .isBoolean()
    .withMessage('External website enabled must be a boolean'),
  body('track_referrals')
    .optional()
    .isBoolean()
    .withMessage('Track referrals must be a boolean'),
  handleValidationErrors,
];

export default handleValidationErrors;
