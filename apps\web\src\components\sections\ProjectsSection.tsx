'use client';

import { motion } from 'framer-motion';
import { EyeIcon, PlayIcon } from '@heroicons/react/24/outline';
import Link from 'next/link';

export function ProjectsSection() {
  const projects = [
    {
      id: 1,
      title: 'Lagos Shopping Mall Virtual Tour',
      category: 'Retail',
      image: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=600&h=400&fit=crop',
      description: 'Complete 360° virtual tour of a premium shopping mall in Victoria Island, Lagos.',
      features: ['360° Photography', 'Interactive Hotspots', 'Mobile Optimized'],
      href: '/projects/lagos-mall'
    },
    {
      id: 2,
      title: 'Abuja Luxury Hotel Experience',
      category: 'Hospitality',
      image: 'https://images.unsplash.com/photo-1566073771259-6a8506099945?w=600&h=400&fit=crop',
      description: 'Immersive virtual experience showcasing luxury accommodations and amenities.',
      features: ['8K Quality', 'VR Compatible', 'Booking Integration'],
      href: '/projects/abuja-hotel'
    },
    {
      id: 3,
      title: 'Port Harcourt Real Estate Showcase',
      category: 'Real Estate',
      image: 'https://images.unsplash.com/photo-1560518883-ce09059eeffa?w=600&h=400&fit=crop',
      description: 'Virtual property tours for premium residential developments.',
      features: ['3D Dollhouse', 'Floor Plans', 'Virtual Staging'],
      href: '/projects/ph-real-estate'
    },
    {
      id: 4,
      title: 'University of Lagos Campus Tour',
      category: 'Education',
      image: 'https://images.unsplash.com/photo-1523050854058-8df90110c9f1?w=600&h=400&fit=crop',
      description: 'Comprehensive virtual campus tour for prospective students.',
      features: ['Interactive Map', 'Department Tours', 'Student Stories'],
      href: '/projects/unilag-tour'
    }
  ];

  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 mb-6">
            Featured <span className="text-blue-600">Projects</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-4xl mx-auto">
            Explore our portfolio of immersive virtual experiences created for leading businesses across Nigeria.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {projects.map((project, index) => (
            <motion.div
              key={project.id}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="group"
            >
              <Link href={project.href} className="block">
                <div className="bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1 overflow-hidden">
                  <div className="relative h-64 overflow-hidden">
                    <img 
                      src={project.image} 
                      alt={project.title}
                      className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                    />
                    <div className="absolute inset-0 bg-black/40 group-hover:bg-black/30 transition-colors duration-300" />
                    
                    {/* Category Badge */}
                    <div className="absolute top-4 left-4">
                      <span className="bg-blue-600 text-white px-3 py-1 rounded-full text-sm font-semibold">
                        {project.category}
                      </span>
                    </div>

                    {/* Play Button */}
                    <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                      <div className="w-16 h-16 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center">
                        <PlayIcon className="w-8 h-8 text-gray-900 ml-1" />
                      </div>
                    </div>

                    {/* View Count */}
                    <div className="absolute bottom-4 right-4 flex items-center text-white text-sm">
                      <EyeIcon className="w-4 h-4 mr-1" />
                      <span>{Math.floor(Math.random() * 5000) + 1000} views</span>
                    </div>
                  </div>

                  <div className="p-6">
                    <h3 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors">
                      {project.title}
                    </h3>
                    <p className="text-gray-600 mb-4 leading-relaxed">
                      {project.description}
                    </p>

                    {/* Features */}
                    <div className="flex flex-wrap gap-2">
                      {project.features.map((feature, idx) => (
                        <span 
                          key={idx}
                          className="bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-xs font-medium"
                        >
                          {feature}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              </Link>
            </motion.div>
          ))}
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          viewport={{ once: true }}
          className="text-center mt-12"
        >
          <Link 
            href="/projects" 
            className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-lg text-lg font-semibold transition-colors inline-block"
          >
            View All Projects
          </Link>
        </motion.div>
      </div>
    </section>
  );
}
