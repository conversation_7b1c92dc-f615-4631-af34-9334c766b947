import axios from 'axios';
import type {
  WhatsAppMessage,
  WhatsAppResponse,
  Product,
  StoreCommunicationConfig
} from '../../../packages/types/dist/index';
import { WhatsAppError } from '@/utils/errors';
import config from '@/config';
import logger from '@/utils/logger';

export class WhatsAppService {
  private apiUrl: string;
  private apiToken: string;

  constructor() {
    this.apiUrl = config.whatsapp.apiUrl;
    this.apiToken = config.whatsapp.apiToken;
  }

  /**
   * Send a product inquiry message via WhatsApp Business API
   */
  async sendProductInquiry(
    storeConfig: StoreCommunicationConfig,
    product: Product,
    customerName: string,
    customerMessage: string,
    customerEmail?: string,
    customerPhone?: string
  ): Promise<WhatsAppResponse> {
    try {
      if (!storeConfig.whatsapp_enabled || !storeConfig.whatsapp_business_number) {
        throw new WhatsAppError('WhatsApp is not enabled for this store');
      }

      const message = this.formatProductInquiryMessage(
        product,
        customerName,
        customerMessage,
        customerEmail,
        customerPhone
      );

      const response = await this.sendMessage(
        storeConfig.whatsapp_business_number,
        message
      );

      logger.info(`WhatsApp inquiry sent for product ${product.id}`, {
        productId: product.id,
        storeId: product.store_id,
        customerName,
        messageId: response.messageId,
      });

      return response;
    } catch (error) {
      logger.error('Failed to send WhatsApp inquiry:', error);
      throw error instanceof WhatsAppError ? error : new WhatsAppError(error.message);
    }
  }

  /**
   * Send a message via WhatsApp Business API
   */
  private async sendMessage(to: string, message: string): Promise<WhatsAppResponse> {
    try {
      // For demo purposes, we'll simulate the API call
      // In production, you would integrate with the actual WhatsApp Business API

      if (!this.apiToken) {
        logger.warn('WhatsApp API token not configured, simulating message send');
        return {
          success: true,
          messageId: `sim_${Date.now()}`,
        };
      }

      const payload = {
        messaging_product: 'whatsapp',
        to: to.replace(/\D/g, ''), // Remove non-digits
        type: 'text',
        text: {
          body: message,
        },
      };

      const response = await axios.post(
        `${this.apiUrl}/messages`,
        payload,
        {
          headers: {
            'Authorization': `Bearer ${this.apiToken}`,
            'Content-Type': 'application/json',
          },
          timeout: 10000,
        }
      );

      if (response.data.messages && response.data.messages[0]) {
        return {
          success: true,
          messageId: response.data.messages[0].id,
        };
      }

      throw new WhatsAppError('Invalid response from WhatsApp API');
    } catch (error) {
      if (axios.isAxiosError(error)) {
        const message = error.response?.data?.error?.message || error.message;
        throw new WhatsAppError(`API request failed: ${message}`);
      }
      throw new WhatsAppError(error.message);
    }
  }

  /**
   * Format product inquiry message for WhatsApp
   */
  private formatProductInquiryMessage(
    product: Product,
    customerName: string,
    customerMessage: string,
    customerEmail?: string,
    customerPhone?: string
  ): string {
    const lines = [
      '🛍️ *New Product Inquiry*',
      '',
      `*Product:* ${product.name}`,
      `*Price:* ${product.currency} ${product.price}`,
      '',
      `*Customer:* ${customerName}`,
    ];

    if (customerEmail) {
      lines.push(`*Email:* ${customerEmail}`);
    }

    if (customerPhone) {
      lines.push(`*Phone:* ${customerPhone}`);
    }

    lines.push(
      '',
      `*Message:*`,
      customerMessage,
      '',
      '---',
      `Sent via VirtualRealTour`
    );

    return lines.join('\n');
  }

  /**
   * Validate WhatsApp phone number format
   */
  static validatePhoneNumber(phoneNumber: string): boolean {
    // Basic validation for international phone numbers
    const phoneRegex = /^\+[1-9]\d{1,14}$/;
    return phoneRegex.test(phoneNumber);
  }

  /**
   * Format phone number for WhatsApp API
   */
  static formatPhoneNumber(phoneNumber: string): string {
    // Remove all non-digit characters except +
    let formatted = phoneNumber.replace(/[^\d+]/g, '');

    // Ensure it starts with +
    if (!formatted.startsWith('+')) {
      formatted = '+' + formatted;
    }

    return formatted;
  }
}

export default WhatsAppService;
