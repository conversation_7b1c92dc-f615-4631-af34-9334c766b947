'use client';

import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { useEffect, useState } from 'react';

export function StatsSection() {
  const [ref, inView] = useInView({ threshold: 0.3, triggerOnce: true });
  const [counts, setCounts] = useState({ projects: 0, clients: 0, tours: 0, cities: 0 });

  const stats = [
    { label: 'Projects Completed', value: 150, suffix: '+' },
    { label: 'Happy Clients', value: 80, suffix: '+' },
    { label: 'Virtual Tours Created', value: 300, suffix: '+' },
    { label: 'Cities Covered', value: 12, suffix: '' }
  ];

  useEffect(() => {
    if (inView) {
      const timers = stats.map((stat, index) => {
        return setTimeout(() => {
          let start = 0;
          const end = stat.value;
          const duration = 2000;
          const increment = end / (duration / 16);

          const timer = setInterval(() => {
            start += increment;
            if (start >= end) {
              setCounts(prev => ({ ...prev, [Object.keys(counts)[index]]: end }));
              clearInterval(timer);
            } else {
              setCounts(prev => ({ ...prev, [Object.keys(counts)[index]]: Math.floor(start) }));
            }
          }, 16);
        }, index * 200);
      });

      return () => timers.forEach(timer => clearTimeout(timer));
    }
  }, [inView]);

  return (
    <section className="py-20 bg-gradient-to-r from-blue-600 to-purple-600">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl sm:text-5xl font-bold text-white mb-6">
            Our Impact Across Nigeria
          </h2>
          <p className="text-xl text-blue-100 max-w-3xl mx-auto">
            Trusted by leading businesses across Lagos, Abuja, Port Harcourt, and beyond.
          </p>
        </motion.div>

        <div ref={ref} className="grid grid-cols-2 md:grid-cols-4 gap-8">
          {stats.map((stat, index) => (
            <motion.div
              key={stat.label}
              initial={{ opacity: 0, scale: 0.5 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="text-center"
            >
              <div className="text-4xl sm:text-5xl lg:text-6xl font-bold text-white mb-2">
                {Object.values(counts)[index]}{stat.suffix}
              </div>
              <div className="text-blue-100 font-medium">
                {stat.label}
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}
