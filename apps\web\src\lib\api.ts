import axios, { AxiosInstance, AxiosResponse } from 'axios';
import type {
  ApiResponse,
  PaginatedResponse,
  Store,
  Product,
  Tour,
  ProductInquiry,
  ReferralAnalytics,
  CreateStoreRequest,
  UpdateStoreRequest,
  CreateProductRequest,
  UpdateProductRequest,
  CreateTourRequest,
  UpdateTourRequest,
  CreateInquiryRequest,
  WhatsAppInquiryRequest,
  SearchFilters,
  PaginationParams,
  DashboardStats,
  AnalyticsTimeframe,
} from '@/types';

class ApiClient {
  private client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001',
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor to add tenant info
    this.client.interceptors.request.use((config) => {
      // Add tenant subdomain to headers for multi-tenant routing
      if (typeof window !== 'undefined') {
        const hostname = window.location.hostname;
        const subdomain = hostname.split('.')[0];
        if (subdomain && subdomain !== 'localhost' && subdomain !== 'www') {
          config.headers['X-Tenant-Subdomain'] = subdomain;
        }
      }
      return config;
    });

    // Response interceptor for error handling
    this.client.interceptors.response.use(
      (response) => response,
      (error) => {
        console.error('API Error:', error.response?.data || error.message);
        return Promise.reject(error);
      }
    );
  }

  // Helper method to handle API responses
  private handleResponse<T>(response: AxiosResponse<ApiResponse<T>>): T {
    if (!response.data.success) {
      throw new Error(response.data.error || 'API request failed');
    }
    return response.data.data!;
  }

  private handlePaginatedResponse<T>(
    response: AxiosResponse<PaginatedResponse<T>>
  ): PaginatedResponse<T> {
    if (!response.data.success) {
      throw new Error(response.data.error || 'API request failed');
    }
    return response.data;
  }

  // Store API methods
  async getStores(params?: PaginationParams): Promise<PaginatedResponse<Store>> {
    const response = await this.client.get<PaginatedResponse<Store>>('/api/stores', {
      params,
    });
    return this.handlePaginatedResponse(response);
  }

  async getStore(storeId: string): Promise<Store> {
    const response = await this.client.get<ApiResponse<Store>>(`/api/stores/${storeId}`);
    return this.handleResponse(response);
  }

  async createStore(data: CreateStoreRequest): Promise<Store> {
    const response = await this.client.post<ApiResponse<Store>>('/api/stores', data);
    return this.handleResponse(response);
  }

  async updateStore(storeId: string, data: UpdateStoreRequest): Promise<Store> {
    const response = await this.client.put<ApiResponse<Store>>(`/api/stores/${storeId}`, data);
    return this.handleResponse(response);
  }

  async deleteStore(storeId: string): Promise<void> {
    await this.client.delete(`/api/stores/${storeId}`);
  }

  // Product API methods
  async getProducts(
    storeId?: string,
    filters?: SearchFilters,
    params?: PaginationParams
  ): Promise<PaginatedResponse<Product>> {
    const queryParams = { ...filters, ...params };
    const url = storeId ? `/api/stores/${storeId}/products` : '/api/products/search';
    const response = await this.client.get<PaginatedResponse<Product>>(url, {
      params: queryParams,
    });
    return this.handlePaginatedResponse(response);
  }

  async getProduct(productId: string): Promise<Product> {
    const response = await this.client.get<ApiResponse<Product>>(`/api/products/${productId}`);
    return this.handleResponse(response);
  }

  async createProduct(data: CreateProductRequest): Promise<Product> {
    const response = await this.client.post<ApiResponse<Product>>('/api/products', data);
    return this.handleResponse(response);
  }

  async updateProduct(productId: string, data: UpdateProductRequest): Promise<Product> {
    const response = await this.client.put<ApiResponse<Product>>(`/api/products/${productId}`, data);
    return this.handleResponse(response);
  }

  async deleteProduct(productId: string): Promise<void> {
    await this.client.delete(`/api/products/${productId}`);
  }

  // Tour API methods
  async getTours(storeId?: string, params?: PaginationParams): Promise<PaginatedResponse<Tour>> {
    const url = storeId ? `/api/stores/${storeId}/tours` : '/api/tours';
    const response = await this.client.get<PaginatedResponse<Tour>>(url, { params });
    return this.handlePaginatedResponse(response);
  }

  async getTour(tourId: string): Promise<Tour> {
    const response = await this.client.get<ApiResponse<Tour>>(`/api/tours/${tourId}`);
    return this.handleResponse(response);
  }

  async createTour(data: CreateTourRequest): Promise<Tour> {
    const response = await this.client.post<ApiResponse<Tour>>('/api/tours', data);
    return this.handleResponse(response);
  }

  async updateTour(tourId: string, data: UpdateTourRequest): Promise<Tour> {
    const response = await this.client.put<ApiResponse<Tour>>(`/api/tours/${tourId}`, data);
    return this.handleResponse(response);
  }

  async deleteTour(tourId: string): Promise<void> {
    await this.client.delete(`/api/tours/${tourId}`);
  }

  // Inquiry API methods
  async getInquiries(
    storeId?: string,
    params?: PaginationParams
  ): Promise<PaginatedResponse<ProductInquiry>> {
    const url = storeId ? `/api/stores/${storeId}/inquiries` : '/api/inquiries';
    const response = await this.client.get<PaginatedResponse<ProductInquiry>>(url, { params });
    return this.handlePaginatedResponse(response);
  }

  async createInquiry(data: CreateInquiryRequest): Promise<ProductInquiry> {
    const response = await this.client.post<ApiResponse<ProductInquiry>>('/api/inquiries', data);
    return this.handleResponse(response);
  }

  async updateInquiryStatus(
    inquiryId: string,
    status: ProductInquiry['status']
  ): Promise<ProductInquiry> {
    const response = await this.client.patch<ApiResponse<ProductInquiry>>(
      `/api/inquiries/${inquiryId}`,
      { status }
    );
    return this.handleResponse(response);
  }

  // WhatsApp API methods
  async sendWhatsAppInquiry(data: WhatsAppInquiryRequest): Promise<{ message_id: string }> {
    const response = await this.client.post<ApiResponse<{ message_id: string }>>(
      '/api/whatsapp/inquiry',
      data
    );
    return this.handleResponse(response);
  }

  // Analytics API methods
  async getDashboardStats(timeframe?: AnalyticsTimeframe): Promise<DashboardStats> {
    const response = await this.client.get<ApiResponse<DashboardStats>>('/api/analytics/dashboard', {
      params: timeframe,
    });
    return this.handleResponse(response);
  }

  async getReferralAnalytics(
    storeId?: string,
    timeframe?: AnalyticsTimeframe,
    params?: PaginationParams
  ): Promise<PaginatedResponse<ReferralAnalytics>> {
    const url = storeId ? `/api/stores/${storeId}/analytics` : '/api/analytics/referrals';
    const response = await this.client.get<PaginatedResponse<ReferralAnalytics>>(url, {
      params: { ...timeframe, ...params },
    });
    return this.handlePaginatedResponse(response);
  }

  // Referral tracking
  async trackReferralClick(productId: string, referralUrl: string): Promise<void> {
    await this.client.post('/api/referrals/track-click', {
      product_id: productId,
      referral_url: referralUrl,
    });
  }

  async generateReferralUrl(productId: string, campaign?: string): Promise<{ referral_url: string }> {
    const response = await this.client.post<ApiResponse<{ referral_url: string }>>(
      '/api/referrals/generate',
      {
        product_id: productId,
        utm_campaign: campaign,
      }
    );
    return this.handleResponse(response);
  }

  // Health check
  async healthCheck(): Promise<{ status: string; timestamp: string }> {
    const response = await this.client.get<ApiResponse<{ status: string; timestamp: string }>>(
      '/api/health'
    );
    return this.handleResponse(response);
  }
}

// Create and export a singleton instance
export const apiClient = new ApiClient();
export default apiClient;
