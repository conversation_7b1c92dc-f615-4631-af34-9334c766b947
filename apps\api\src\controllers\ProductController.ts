import { Request, Response } from 'express';
import ProductService from '@/services/ProductService';
import WhatsAppService from '@/services/WhatsAppService';
import ReferralService from '@/services/ReferralService';
import ResponseHelper from '@/utils/response';
import { asyncHandler } from '@/middleware/errorHandler';
import type {
  WhatsAppInquiryRequest,
  ExternalReferralRequest,
  StoreCommunicationConfig
} from '@/types';
import logger from '@/utils/logger';

export class ProductController {
  private productService: ProductService;
  private whatsappService: WhatsAppService;
  private referralService: ReferralService;

  constructor() {
    this.productService = new ProductService();
    this.whatsappService = new WhatsAppService();
    this.referralService = new ReferralService();
  }

  /**
   * Get products for a store
   */
  getStoreProducts = asyncHandler(async (req: Request, res: Response) => {
    const { storeId } = req.params;
    const tenantId = req.tenant!.id;

    const options = {
      page: parseInt(req.query.page as string) || 1,
      limit: parseInt(req.query.limit as string) || 20,
      sortBy: req.query.sortBy as string || 'created_at',
      sortOrder: req.query.sortOrder as 'asc' | 'desc' || 'desc',
      filters: {},
    };

    // Add search filter if provided
    if (req.query.search) {
      options.filters = {
        OR: [
          { name: { contains: req.query.search as string, mode: 'insensitive' } },
          { description: { contains: req.query.search as string, mode: 'insensitive' } },
        ],
      };
    }

    const result = await this.productService.getStoreProducts(storeId, tenantId, options);

    return ResponseHelper.paginated(
      res,
      result.products,
      result.pagination.page,
      result.pagination.limit,
      result.pagination.total,
      'Products retrieved successfully'
    );
  });

  /**
   * Get a single product
   */
  getProduct = asyncHandler(async (req: Request, res: Response) => {
    const { productId } = req.params;
    const tenantId = req.tenant!.id;

    const product = await this.productService.getProduct(productId, tenantId);

    return ResponseHelper.success(res, product, 'Product retrieved successfully');
  });

  /**
   * Search products across all stores
   */
  searchProducts = asyncHandler(async (req: Request, res: Response) => {
    const { q: searchTerm } = req.query;
    const tenantId = req.tenant!.id;

    if (!searchTerm || typeof searchTerm !== 'string') {
      return ResponseHelper.badRequest(res, 'Search term is required');
    }

    const options = {
      page: parseInt(req.query.page as string) || 1,
      limit: parseInt(req.query.limit as string) || 20,
      sortBy: req.query.sortBy as string || 'created_at',
      sortOrder: req.query.sortOrder as 'asc' | 'desc' || 'desc',
    };

    const result = await this.productService.searchProducts(tenantId, searchTerm, options);

    return ResponseHelper.paginated(
      res,
      result.products,
      result.pagination.page,
      result.pagination.limit,
      result.pagination.total,
      `Found ${result.pagination.total} products for "${searchTerm}"`
    );
  });

  /**
   * Send WhatsApp inquiry for a product
   */
  sendWhatsAppInquiry = asyncHandler(async (req: Request, res: Response) => {
    const { productId } = req.params;
    const tenantId = req.tenant!.id;
    const inquiryData: WhatsAppInquiryRequest = req.body;

    // Get product details
    const product = await this.productService.getProduct(productId, tenantId);

    if (!(product as any).whatsappInquiryEnabled) {
      return ResponseHelper.badRequest(res, 'WhatsApp inquiries are not enabled for this product');
    }

    const storeConfig = (product as any).store.communicationConfig as StoreCommunicationConfig;

    if (!storeConfig.whatsapp_enabled) {
      return ResponseHelper.badRequest(res, 'WhatsApp is not enabled for this store');
    }

    // Send WhatsApp message
    const whatsappResponse = await this.whatsappService.sendProductInquiry(
      storeConfig,
      product,
      inquiryData.customer_name,
      inquiryData.message,
      inquiryData.customer_email,
      inquiryData.customer_phone
    );

    // Create inquiry record
    const inquiry = await this.productService.createProductInquiry(productId, tenantId, {
      customer_name: inquiryData.customer_name,
      customer_email: inquiryData.customer_email,
      customer_phone: inquiryData.customer_phone,
      message: inquiryData.message,
      inquiry_type: 'whatsapp',
    });

    // Update inquiry with WhatsApp message ID if available
    if (whatsappResponse.messageId) {
      await this.productService.updateInquiryStatus(inquiry.id, tenantId, 'sent');
    }

    logger.info(`WhatsApp inquiry sent for product ${productId}`, {
      inquiryId: inquiry.id,
      messageId: whatsappResponse.messageId,
    });

    return ResponseHelper.success(res, {
      inquiry,
      whatsappResponse,
    }, 'WhatsApp inquiry sent successfully');
  });

  /**
   * Generate external referral URL for a product
   */
  generateExternalReferral = asyncHandler(async (req: Request, res: Response) => {
    const { productId } = req.params;
    const tenantId = req.tenant!.id;
    const referralData: ExternalReferralRequest = req.body;

    // Get product details
    const product = await this.productService.getProduct(productId, tenantId);

    if (!(product as any).referralTrackingEnabled) {
      return ResponseHelper.badRequest(res, 'Referral tracking is not enabled for this product');
    }

    const storeConfig = (product as any).store.communicationConfig as StoreCommunicationConfig;

    if (!storeConfig.external_website_enabled) {
      return ResponseHelper.badRequest(res, 'External website is not enabled for this store');
    }

    // Generate tracked referral URL
    const result = await this.referralService.generateReferralUrl(
      productId,
      tenantId,
      referralData.utm_source,
      referralData.utm_medium,
      referralData.utm_campaign
    );

    // Create inquiry record for tracking
    if (referralData.customer_email) {
      await this.productService.createProductInquiry(productId, tenantId, {
        customer_name: 'External Referral',
        customer_email: referralData.customer_email,
        message: 'Customer redirected to external website',
        inquiry_type: 'external_referral',
      });
    }

    logger.info(`External referral generated for product ${productId}`, {
      trackingId: result.trackingId,
      referralUrl: result.referralUrl,
    });

    return ResponseHelper.success(res, result, 'External referral URL generated successfully');
  });

  /**
   * Track referral click
   */
  trackReferralClick = asyncHandler(async (req: Request, res: Response) => {
    const { productId } = req.params;
    const tenantId = req.tenant!.id;
    const { utm_source, utm_medium, utm_campaign } = req.query;

    if (!utm_source || !utm_medium || !utm_campaign) {
      return ResponseHelper.badRequest(res, 'UTM parameters are required');
    }

    await this.referralService.trackClick(
      productId,
      tenantId,
      utm_source as string,
      utm_medium as string,
      utm_campaign as string
    );

    return ResponseHelper.success(res, { tracked: true }, 'Referral click tracked successfully');
  });

  /**
   * Get product referral analytics
   */
  getProductAnalytics = asyncHandler(async (req: Request, res: Response) => {
    const { productId } = req.params;
    const tenantId = req.tenant!.id;

    const analytics = await this.referralService.getProductReferralAnalytics(productId, tenantId);

    return ResponseHelper.success(res, analytics, 'Product analytics retrieved successfully');
  });
}

export default ProductController;
