import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seed...');

  // Create demo tenant
  const demoTenantConfig = {
    siteName: 'Demo Virtual Mall',
    logoUrl: 'https://example.com/logo.png',
    primaryColor: '#3b82f6',
    secondaryColor: '#64748b',
    features: ['tours', 'whatsapp', 'referrals'],
    communicationChannels: ['whatsapp', 'external_website'],
    whatsappConfig: {
      businessNumber: '+1234567890',
      apiKey: 'demo_api_key',
      enabled: true,
    },
    externalWebsite: {
      url: 'https://demo-store.com',
      enabled: true,
      trackReferrals: true,
    },
  };

  const tenant = await prisma.tenant.upsert({
    where: { subdomain: 'demo' },
    update: {},
    create: {
      name: 'Demo Tenant',
      subdomain: 'demo',
      domain: 'demo.virtualrealtour.com',
      config: demoTenantConfig,
      status: 'active',
    },
  });

  console.log('✅ Created tenant:', tenant.name);

  // Create demo store
  const storeCommunicationConfig = {
    whatsapp_business_number: '+1234567890',
    whatsapp_api_key: 'demo_store_api_key',
    whatsapp_enabled: true,
    external_website_url: 'https://demo-electronics-store.com',
    external_website_enabled: true,
    track_referrals: true,
  };

  const store = await prisma.store.upsert({
    where: {
      tenantId_slug: {
        tenantId: tenant.id,
        slug: 'electronics-store'
      }
    },
    update: {},
    create: {
      tenantId: tenant.id,
      name: 'Electronics Store',
      description: 'Premium electronics and gadgets',
      slug: 'electronics-store',
      logoUrl: 'https://example.com/electronics-logo.png',
      bannerUrl: 'https://example.com/electronics-banner.jpg',
      status: 'active',
      communicationConfig: storeCommunicationConfig,
    },
  });

  console.log('✅ Created store:', store.name);

  // Create demo products
  const products = [
    {
      name: 'iPhone 15 Pro',
      description: 'Latest iPhone with advanced camera system',
      price: 999.99,
      currency: 'USD',
      images: [
        'https://example.com/iphone-1.jpg',
        'https://example.com/iphone-2.jpg',
      ],
      has3dModel: true,
      vrReady: true,
      externalUrl: 'https://demo-electronics-store.com/iphone-15-pro',
    },
    {
      name: 'MacBook Pro 16"',
      description: 'Powerful laptop for professionals',
      price: 2499.99,
      currency: 'USD',
      images: [
        'https://example.com/macbook-1.jpg',
        'https://example.com/macbook-2.jpg',
      ],
      has3dModel: true,
      vrReady: false,
      externalUrl: 'https://demo-electronics-store.com/macbook-pro-16',
    },
    {
      name: 'AirPods Pro',
      description: 'Wireless earbuds with noise cancellation',
      price: 249.99,
      currency: 'USD',
      images: [
        'https://example.com/airpods-1.jpg',
      ],
      has3dModel: false,
      vrReady: false,
      externalUrl: 'https://demo-electronics-store.com/airpods-pro',
    },
  ];

  for (const productData of products) {
    const product = await prisma.product.create({
      data: {
        ...productData,
        storeId: store.id,
        tenantId: tenant.id,
        whatsappInquiryEnabled: true,
        referralTrackingEnabled: true,
        status: 'active',
      },
    });
    console.log('✅ Created product:', product.name);
  }

  // Create demo tour
  const tour = await prisma.tour.create({
    data: {
      tenantId: tenant.id,
      storeId: store.id,
      title: 'Electronics Store Virtual Tour',
      description: 'Explore our premium electronics collection',
      tourType: 'panorama',
      tourUrl: 'https://example.com/tour/electronics-store',
      hotspots: [
        {
          id: 'hotspot-1',
          position: { x: 0.5, y: 0.3, z: 0.8 },
          type: 'product',
          title: 'iPhone 15 Pro',
          description: 'Latest iPhone model',
          action_type: 'whatsapp_inquiry',
        },
        {
          id: 'hotspot-2',
          position: { x: 0.7, y: 0.4, z: 0.6 },
          type: 'product',
          title: 'MacBook Pro',
          description: 'Professional laptop',
          action_type: 'external_link',
        },
      ],
      status: 'active',
    },
  });

  console.log('✅ Created tour:', tour.title);

  // Create demo user
  const user = await prisma.user.create({
    data: {
      tenantId: tenant.id,
      email: '<EMAIL>',
      name: 'Demo Admin',
      role: 'tenant_admin',
      status: 'active',
    },
  });

  console.log('✅ Created user:', user.email);

  console.log('🎉 Database seeded successfully!');
}

main()
  .catch((e) => {
    console.error('❌ Error seeding database:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
