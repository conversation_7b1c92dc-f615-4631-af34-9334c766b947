# Lovable.dev AI Instructions - VirtualRealTour

## Project Overview
You are designing the UI/UX for VirtualRealTour, a white-label virtual shopping mall platform. Your role is to create stunning, interactive, and immersive user interfaces that will be converted to Next.js components.

## Design Philosophy

### Primary Inspiration: https://lusion.co/
Study this website's approach to:
- **Fluid Animations**: Smooth, physics-based transitions
- **Interactive Elements**: Hover effects, scroll-triggered animations
- **Immersive Storytelling**: Progressive disclosure of content
- **Premium Feel**: High-quality visuals and micro-interactions
- **Performance**: Optimized animations that don't compromise user experience

### Design Principles
1. **Immersion First**: Every interaction should feel like stepping into a virtual world
2. **Progressive Enhancement**: Graceful degradation from immersive to standard experiences
3. **Accessibility**: Ensure all users can navigate and interact regardless of device or ability
4. **Performance**: Prioritize smooth 60fps animations and quick load times
5. **Whitelabel Ready**: Designs must be easily customizable for different brands

## Visual Design System

### Color Palette
```css
/* Primary Colors (customizable via props) */
--primary-50: #f0f9ff;
--primary-500: #3b82f6;
--primary-900: #1e3a8a;

/* Neutral Colors */
--neutral-50: #f8fafc;
--neutral-100: #f1f5f9;
--neutral-500: #64748b;
--neutral-900: #0f172a;

/* Accent Colors */
--accent-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
--glass-bg: rgba(255, 255, 255, 0.1);
--glass-border: rgba(255, 255, 255, 0.2);
```

### Typography Scale
```css
/* Display Text */
--text-6xl: 3.75rem; /* 60px */
--text-5xl: 3rem;    /* 48px */
--text-4xl: 2.25rem; /* 36px */

/* Headings */
--text-3xl: 1.875rem; /* 30px */
--text-2xl: 1.5rem;   /* 24px */
--text-xl: 1.25rem;   /* 20px */

/* Body Text */
--text-lg: 1.125rem; /* 18px */
--text-base: 1rem;   /* 16px */
--text-sm: 0.875rem; /* 14px */
```

### Spacing System
```css
/* Consistent spacing scale */
--space-1: 0.25rem;  /* 4px */
--space-2: 0.5rem;   /* 8px */
--space-4: 1rem;     /* 16px */
--space-8: 2rem;     /* 32px */
--space-16: 4rem;    /* 64px */
--space-32: 8rem;    /* 128px */
```

## Core Components to Design

### 1. Hero Section
**Purpose**: First impression that showcases the virtual shopping experience

**Requirements**:
- Animated 3D elements or floating cards
- Smooth parallax scrolling effects
- Call-to-action that transforms on hover
- Background video or animated gradient
- Responsive design for all devices

**Animation Details**:
```javascript
// Example animation config
const heroAnimations = {
  fadeIn: { duration: 1.2, ease: "power2.out" },
  floatingCards: { 
    y: "random(-20, 20)",
    rotation: "random(-5, 5)",
    duration: 3,
    repeat: -1,
    yoyo: true
  },
  ctaHover: {
    scale: 1.05,
    boxShadow: "0 20px 40px rgba(0,0,0,0.2)",
    duration: 0.3
  }
};
```

### 2. Store Navigation Interface
**Purpose**: Mall-like navigation system for browsing different stores

**Features**:
- Interactive store map with zoom/pan capabilities
- Store category filters with smooth transitions
- Search functionality with real-time results
- Store preview cards with hover effects
- Breadcrumb navigation

**Visual Elements**:
- Glassmorphism design for navigation cards
- Smooth category transitions
- Loading skeletons for store previews
- Micro-interactions for all clickable elements

### 3. Virtual Tour Viewer
**Purpose**: Container for 360°, 3D, and VR tour experiences

**Requirements**:
- Full-screen tour viewer with controls
- Floating UI elements that don't obstruct the tour
- Progress indicators for tour loading
- Hotspot indicators with pulsing animations
- Tour navigation breadcrumbs

**UI Controls**:
```javascript
// Tour viewer controls
const tourControls = [
  { icon: "expand", action: "fullscreen", position: "top-right" },
  { icon: "info", action: "show-details", position: "top-left" },
  { icon: "share", action: "share-tour", position: "bottom-right" },
  { icon: "exit", action: "close-tour", position: "top-right" }
];
```

### 4. Product Cards & Grids
**Purpose**: Showcase products with rich media and interaction

**Features**:
- Image zoom on hover
- 3D model preview buttons
- Quick add-to-cart with animation
- Wishlist toggle with heart animation
- Price display with currency switching
- Stock status indicators

**Interaction States**:
- Default: Clean, minimal design
- Hover: Elevated with shadow, image zoom
- Active: Highlighted border, elevated further
- Loading: Skeleton animation
- Error: Gentle error state with retry option

### 5. Product Inquiry & Referral Actions
**Purpose**: Streamlined customer-to-store communication

**Features**:
- WhatsApp inquiry button with instant messaging
- External website redirection with tracking
- Product information sharing
- Inquiry form with customer details
- Store contact information display
- Referral success confirmation

### 6. Admin Dashboard
**Purpose**: Store management interface for tenants

**Features**:
- Analytics dashboard with animated charts (inquiries, referrals)
- Tour upload interface with drag-and-drop
- Product management with bulk actions
- Store customization panel
- WhatsApp configuration management
- External website URL management
- Inquiry management system

**Design Focus**:
- Clean, professional interface
- Data visualization with smooth animations
- Clear action buttons and navigation
- Responsive table designs
- Form validation with helpful error messages

## Animation Guidelines

### Micro-Interactions
```javascript
// Button hover animation
const buttonHover = {
  scale: 1.02,
  backgroundColor: "var(--primary-600)",
  transition: { duration: 0.2, ease: "easeOut" }
};

// Card hover animation
const cardHover = {
  y: -8,
  boxShadow: "0 25px 50px rgba(0,0,0,0.15)",
  transition: { duration: 0.3, ease: "easeOut" }
};

// Loading animation
const loadingSpinner = {
  rotate: 360,
  transition: { duration: 1, repeat: Infinity, ease: "linear" }
};
```

### Page Transitions
```javascript
// Page enter animation
const pageEnter = {
  opacity: 0,
  y: 20,
  animate: { opacity: 1, y: 0 },
  transition: { duration: 0.6, ease: "easeOut" }
};

// Stagger children animation
const staggerChildren = {
  animate: { transition: { staggerChildren: 0.1 } }
};
```

### Scroll-Triggered Animations
```javascript
// Reveal animation on scroll
const revealOnScroll = {
  initial: { opacity: 0, y: 50 },
  whileInView: { opacity: 1, y: 0 },
  transition: { duration: 0.8 },
  viewport: { once: true, margin: "-100px" }
};
```

## Responsive Design Strategy

### Breakpoints
```css
/* Mobile First Approach */
--breakpoint-sm: 640px;   /* Small tablets */
--breakpoint-md: 768px;   /* Tablets */
--breakpoint-lg: 1024px;  /* Laptops */
--breakpoint-xl: 1280px;  /* Desktops */
--breakpoint-2xl: 1536px; /* Large desktops */
```

### Mobile Optimizations
- Touch-friendly button sizes (min 44px)
- Simplified navigation with hamburger menu
- Swipe gestures for tour navigation
- Optimized image sizes for mobile connections
- Reduced animation complexity on mobile devices

### Tablet Considerations
- Landscape and portrait orientations
- Touch and hover state management
- Sidebar navigation that converts to drawer
- Grid layouts that adapt to screen size

### Desktop Enhancements
- Mouse hover effects and animations
- Keyboard navigation support
- Multi-column layouts
- Advanced filtering and sorting options
- Picture-in-picture tour viewing

## Accessibility Requirements

### WCAG 2.1 AA Compliance
- Color contrast ratios of 4.5:1 for normal text
- Color contrast ratios of 3:1 for large text
- Focus indicators for all interactive elements
- Alt text for all images and icons
- Semantic HTML structure

### Keyboard Navigation
```javascript
// Keyboard navigation handler
const keyboardNavigation = {
  'Tab': 'next-focusable-element',
  'Shift+Tab': 'previous-focusable-element',
  'Enter': 'activate-element',
  'Space': 'activate-button',
  'Escape': 'close-modal-or-drawer'
};
```

### Screen Reader Support
- ARIA labels for complex interactions
- Live regions for dynamic content updates
- Descriptive text for tour experiences
- Table headers and captions
- Form labels and error messages

## Performance Optimization

### Image Optimization
- WebP format with fallbacks
- Lazy loading for images below the fold
- Responsive image sizing
- Progressive JPEG for large images
- SVG optimization for icons

### Animation Performance
```javascript
// Use transform and opacity for better performance
const performantAnimation = {
  transform: "translateY(0px) scale(1)",
  opacity: 1,
  transition: "transform 0.3s ease, opacity 0.3s ease"
};

// Avoid animating properties that trigger layout
const avoidTheseProperties = [
  'width', 'height', 'padding', 'margin', 'border'
];
```

### Bundle Size Optimization
- Tree-shake unused CSS
- Split animations into separate chunks
- Use CSS transforms instead of JavaScript where possible
- Implement virtual scrolling for large lists

## Component Library Structure

### Base Components
```
components/
├── ui/
│   ├── Button/
│   ├── Card/
│   ├── Input/
│   ├── Modal/
│   └── Spinner/
├── layout/
│   ├── Header/
│   ├── Footer/
│   ├── Sidebar/
│   └── Container/
├── tour/
│   ├── TourViewer/
│   ├── TourControls/
│   ├── HotspotMarker/
│   └── TourProgress/
└── communication/
    ├── ProductCard/
    ├── WhatsAppInquiry/
    ├── ExternalLinkButton/
    ├── InquiryForm/
    └── ReferralTracking/
```

### Component Props Interface
```typescript
interface ComponentProps {
  // Visual customization
  variant?: 'primary' | 'secondary' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  theme?: 'light' | 'dark';
  
  // Animation controls
  animate?: boolean;
  animationDelay?: number;
  
  // Accessibility
  ariaLabel?: string;
  ariaDescribedBy?: string;
  
  // Custom styling
  className?: string;
  style?: React.CSSProperties;
}
```

## Design Tokens for White-Label Support

### Theme Configuration
```javascript
const themeConfig = {
  colors: {
    primary: 'var(--brand-primary, #3b82f6)',
    secondary: 'var(--brand-secondary, #64748b)',
    accent: 'var(--brand-accent, #8b5cf6)'
  },
  fonts: {
    heading: 'var(--font-heading, "Inter", sans-serif)',
    body: 'var(--font-body, "Inter", sans-serif)'
  },
  spacing: {
    unit: 'var(--spacing-unit, 1rem)'
  },
  borderRadius: {
    base: 'var(--border-radius, 0.5rem)'
  }
};
```

### Dynamic Theming
```css
/* CSS Custom Properties for theming */
:root {
  --brand-primary: #3b82f6;
  --brand-secondary: #64748b;
  --brand-accent: #8b5cf6;
  --brand-logo: url('/default-logo.svg');
  --brand-font-heading: 'Inter', sans-serif;
  --brand-font-body: 'Inter', sans-serif;
}

/* Dark theme support */
[data-theme="dark"] {
  --bg-primary: #0f172a;
  --bg-secondary: #1e293b;
  --text-primary: #f8fafc;
  --text-secondary: #cbd5e1;
}
```

## Testing & Quality Assurance

### Visual Testing
- Cross-browser compatibility (Chrome, Firefox, Safari, Edge)
- Device testing (iPhone, Android, iPad, Desktop)
- Animation performance profiling
- Accessibility testing with screen readers
- Color contrast validation

### User Experience Testing
```javascript
// A/B testing configuration
const abTestConfig = {
  heroVersion: {
    A: 'video-background',
    B: 'animated-gradient'
  },
  ctaButton: {
    A: 'explore-now',
    B: 'start-shopping'
  }
};
```

### Performance Metrics
- First Contentful Paint (FCP) < 1.5s
- Largest Contentful Paint (LCP) < 2.5s
- Cumulative Layout Shift (CLS) < 0.1
- First Input Delay (FID) < 100ms

## Component Development Workflow

### 1. Design Phase
- Create component wireframes and mockups
- Define interaction states and animations
- Plan responsive behavior
- Document accessibility requirements

### 2. Development Phase
```javascript
// Component development template
const ComponentName = ({ 
  variant = 'primary',
  size = 'md',
  animate = true,
  children,
  ...props 
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const baseClasses = 'component-base';
  const variantClasses = `component-${variant}`;
  const sizeClasses = `component-${size}`;
  
  const animationProps = animate ? {
    whileHover: { scale: 1.02 },
    whileTap: { scale: 0.98 },
    transition: { duration: 0.2 }
  } : {};

  return (
    <motion.div
      className={`${baseClasses} ${variantClasses} ${sizeClasses}`}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
      {...animationProps}
      {...props}
    >
      {children}
    </motion.div>
  );
};
```

### 3. Testing Phase
- Component isolation testing
- Integration with parent components
- Animation performance testing
- Accessibility compliance verification

## Brand Customization Guidelines

### Logo Integration
```javascript
// Dynamic logo component
const BrandLogo = ({ 
  variant = 'default',
  size = 'md',
  customLogo = null 
}) => {
  const logoSrc = customLogo || 'var(--brand-logo)';
  const altText = 'var(--brand-name, "VirtualRealTour")';
  
  return (
    <img 
      src={logoSrc}
      alt={altText}
      className={`logo logo-${variant} logo-${size}`}
    />
  );
};
```

### Color Scheme Adaptation
```css
/* Automatic color scheme adaptation */
.component-primary {
  background: var(--brand-primary);
  color: var(--brand-on-primary);
  border: 1px solid var(--brand-primary-border);
}

.component-primary:hover {
  background: var(--brand-primary-hover);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px var(--brand-primary-shadow);
}
```

### Typography Customization
```css
.heading-primary {
  font-family: var(--brand-font-heading);
  font-weight: var(--brand-heading-weight, 700);
  letter-spacing: var(--brand-heading-spacing, -0.025em);
  line-height: var(--brand-heading-line-height, 1.2);
}

.body-text {
  font-family: var(--brand-font-body);
  font-weight: var(--brand-body-weight, 400);
  line-height: var(--brand-body-line-height, 1.6);
}
```

## Advanced Animation Techniques

### Physics-Based Animations
```javascript
// Spring animations for natural feel
const springConfig = {
  type: "spring",
  stiffness: 300,
  damping: 30
};

const physicsHover = {
  scale: 1.05,
  rotate: [0, 1, -1, 0],
  transition: springConfig
};
```

### Gesture-Based Interactions
```javascript
// Swipe gestures for mobile
const swipeHandlers = {
  onSwipeLeft: () => nextSlide(),
  onSwipeRight: () => previousSlide(),
  onPinch: (scale) => zoomTour(scale),
  onRotate: (angle) => rotateTour(angle)
};
```

### Scroll-Linked Animations
```javascript
// Parallax scrolling effects
const parallaxConfig = {
  initial: { y: 0 },
  animate: { y: scrollY * -0.5 },
  transition: { type: "tween", ease: "linear" }
};
```

## Error States & Loading States

### Loading Animations
```javascript
// Skeleton loading for content
const SkeletonLoader = ({ lines = 3, height = "1rem" }) => (
  <div className="animate-pulse space-y-2">
    {Array.from({ length: lines }).map((_, i) => (
      <div 
        key={i}
        className="bg-gray-200 rounded"
        style={{ height, width: `${100 - (i * 10)}%` }}
      />
    ))}
  </div>
);

// Spinner for actions
const LoadingSpinner = ({ size = "md" }) => (
  <motion.div
    className={`spinner spinner-${size}`}
    animate={{ rotate: 360 }}
    transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
  />
);
```

### Error States
```javascript
// Gentle error handling
const ErrorState = ({ 
  title = "Something went wrong",
  message = "Please try again later",
  onRetry = null 
}) => (
  <motion.div
    initial={{ opacity: 0, scale: 0.9 }}
    animate={{ opacity: 1, scale: 1 }}
    className="error-state text-center p-8"
  >
    <div className="error-icon mb-4">⚠️</div>
    <h3 className="text-lg font-semibold text-gray-900 mb-2">{title}</h3>
    <p className="text-gray-600 mb-4">{message}</p>
    {onRetry && (
      <button onClick={onRetry} className="btn btn-primary">
        Try Again
      </button>
    )}
  </motion.div>
);
```

## Final Delivery Checklist

### Code Quality
- [ ] All components are TypeScript-ready
- [ ] Props interfaces are well-defined
- [ ] Default props are provided
- [ ] Error boundaries are implemented
- [ ] Loading states are handled

### Design Quality
- [ ] Animations are smooth and purposeful
- [ ] Responsive design works on all breakpoints
- [ ] Color contrast meets WCAG standards
- [ ] Typography hierarchy is clear
- [ ] Interactive elements provide feedback

### Performance
- [ ] Bundle size is optimized
- [ ] Images are properly compressed
- [ ] Animations use GPU acceleration
- [ ] Critical CSS is inlined
- [ ] Lazy loading is implemented

### Accessibility
- [ ] Keyboard navigation works
- [ ] Screen readers can navigate
- [ ] Focus indicators are visible
- [ ] ARIA labels are provided
- [ ] Motion preferences are respected

### White-Label Readiness
- [ ] All brand elements are configurable
- [ ] CSS custom properties are used
- [ ] Component variants support different themes
- [ ] Documentation includes customization guide
- [ ] Theme switching works seamlessly

Remember: Every component should feel like it belongs in a premium, immersive virtual shopping experience while remaining accessible and performant across all devices and user contexts.