import { Request, Response, NextFunction } from 'express';
import { CustomError } from '@/utils/errors';
import logger from '@/utils/logger';
import ResponseHelper from '@/utils/response';
import config from '@/config';

/**
 * Global error handling middleware
 */
export function errorHandler(
  error: Error,
  req: Request,
  res: Response,
  next: NextFunction
) {
  // Log the error
  logger.error('Error occurred:', {
    message: error.message,
    stack: error.stack,
    url: req.url,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    tenantId: req.tenant?.id,
    userId: req.user?.userId,
  });

  // Handle custom application errors
  if (error instanceof CustomError) {
    return ResponseHelper.error(res, error.message, error.statusCode);
  }

  // Handle Prisma errors
  if (error.name === 'PrismaClientKnownRequestError') {
    return handlePrismaError(error as any, res);
  }

  // Handle JWT errors
  if (error.name === 'JsonWebTokenError') {
    return ResponseHelper.unauthorized(res, 'Invalid token');
  }

  if (error.name === 'TokenExpiredError') {
    return ResponseHelper.unauthorized(res, 'Token expired');
  }

  // Handle validation errors from express-validator
  if (error.name === 'ValidationError') {
    return ResponseHelper.badRequest(res, error.message);
  }

  // Handle syntax errors (malformed JSON, etc.)
  if (error instanceof SyntaxError && 'body' in error) {
    return ResponseHelper.badRequest(res, 'Invalid JSON in request body');
  }

  // Default to 500 server error
  const message = config.nodeEnv === 'production' 
    ? 'Internal server error' 
    : error.message;

  return ResponseHelper.internalError(res, message);
}

/**
 * Handle Prisma-specific errors
 */
function handlePrismaError(error: any, res: Response) {
  switch (error.code) {
    case 'P2002':
      // Unique constraint violation
      const field = error.meta?.target?.[0] || 'field';
      return ResponseHelper.conflict(res, `${field} already exists`);
    
    case 'P2025':
      // Record not found
      return ResponseHelper.notFound(res, 'Record not found');
    
    case 'P2003':
      // Foreign key constraint violation
      return ResponseHelper.badRequest(res, 'Invalid reference to related record');
    
    case 'P2014':
      // Required relation violation
      return ResponseHelper.badRequest(res, 'Required relation missing');
    
    default:
      logger.error('Unhandled Prisma error:', error);
      return ResponseHelper.internalError(res, 'Database error occurred');
  }
}

/**
 * Handle 404 errors for unmatched routes
 */
export function notFoundHandler(req: Request, res: Response) {
  return ResponseHelper.notFound(res, `Route ${req.method} ${req.path} not found`);
}

/**
 * Async error wrapper to catch async errors in route handlers
 */
export function asyncHandler(fn: Function) {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
}

export default errorHandler;
