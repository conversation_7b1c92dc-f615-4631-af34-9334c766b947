// Core entity types
export interface Tenant {
  id: string;
  name: string;
  subdomain: string;
  domain?: string;
  config: TenantConfig;
  status: 'active' | 'inactive' | 'suspended';
  created_at: Date;
  updated_at: Date;
}

export interface TenantConfig {
  siteName: string;
  logoUrl: string;
  primaryColor: string;
  secondaryColor: string;
  features: string[];
  communicationChannels: string[];
  whatsappConfig: {
    businessNumber: string;
    apiKey: string;
    enabled: boolean;
  };
  externalWebsite: {
    url: string;
    enabled: boolean;
    trackReferrals: boolean;
  };
}

export interface Store {
  id: string;
  tenant_id: string;
  name: string;
  description: string;
  slug: string;
  logo_url?: string;
  banner_url?: string;
  status: 'active' | 'inactive';
  communication_config: StoreCommunicationConfig;
  created_at: Date;
  updated_at: Date;
}

export interface StoreCommunicationConfig {
  whatsapp_business_number?: string;
  whatsapp_api_key?: string;
  whatsapp_enabled: boolean;
  external_website_url?: string;
  external_website_enabled: boolean;
  track_referrals: boolean;
}

export interface Product {
  id: string;
  store_id: string;
  tenant_id: string;
  name: string;
  description: string;
  price: number;
  currency: string;
  images: string[];
  has_3d_model: boolean;
  vr_ready: boolean;
  video_360_url?: string;
  external_url?: string;
  whatsapp_inquiry_enabled: boolean;
  referral_tracking_enabled: boolean;
  metadata: Record<string, any>;
  status: 'active' | 'inactive' | 'draft';
  created_at: Date;
  updated_at: Date;
}

export interface Tour {
  id: string;
  tenant_id: string;
  store_id: string;
  title: string;
  description: string;
  tour_type: 'panorama' | '3d' | 'video' | 'embed';
  tour_url: string;
  hotspots: Hotspot[];
  metadata: Record<string, any>;
  status: 'active' | 'inactive' | 'draft';
  created_at: Date;
  updated_at: Date;
}

export interface Hotspot {
  id: string;
  position: { x: number; y: number; z?: number };
  type: 'product' | 'info' | 'navigation';
  title: string;
  description?: string;
  product_id?: string;
  action_type: 'info' | 'whatsapp_inquiry' | 'external_link' | 'details';
  action_data?: Record<string, any>;
}

export interface ProductInquiry {
  id: string;
  product_id: string;
  store_id: string;
  tenant_id: string;
  customer_name: string;
  customer_email: string;
  customer_phone?: string;
  inquiry_type: 'whatsapp' | 'external_referral';
  message: string;
  status: 'sent' | 'responded' | 'converted' | 'closed';
  whatsapp_message_id?: string;
  referral_url?: string;
  created_at: Date;
  updated_at: Date;
}

export interface ReferralAnalytics {
  id: string;
  product_id: string;
  store_id: string;
  tenant_id: string;
  referral_url: string;
  clicks: number;
  conversions: number;
  revenue_tracked?: number;
  utm_source: string;
  utm_medium: string;
  utm_campaign: string;
  created_at: Date;
  updated_at: Date;
}

// User and authentication types
export interface User {
  id: string;
  tenant_id: string;
  email: string;
  name: string;
  role: UserRole;
  status: 'active' | 'inactive';
  created_at: Date;
  updated_at: Date;
}

export type UserRole = 'super_admin' | 'tenant_admin' | 'store_owner' | 'customer';

export interface JWTPayload {
  userId: string;
  tenantId: string;
  role: UserRole;
  permissions: string[];
  iat: number;
  exp: number;
}

// API request/response types
export interface CreateProductInquiryRequest {
  customer_name: string;
  customer_email: string;
  customer_phone?: string;
  message: string;
  inquiry_type: 'whatsapp' | 'external_referral';
}

export interface WhatsAppInquiryRequest {
  customer_name: string;
  customer_email: string;
  customer_phone?: string;
  message: string;
}

export interface ExternalReferralRequest {
  customer_email?: string;
  utm_source?: string;
  utm_medium?: string;
  utm_campaign?: string;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Service interfaces
export interface WhatsAppMessage {
  to: string;
  message: string;
  productInfo?: {
    name: string;
    price: string;
    url: string;
  };
}

export interface WhatsAppResponse {
  success: boolean;
  messageId?: string;
  error?: string;
}

export interface ReferralTrackingResult {
  referralUrl: string;
  trackingId: string;
  analytics: {
    clicks: number;
    conversions: number;
  };
}

// Error types
export interface AppError extends Error {
  statusCode: number;
  isOperational: boolean;
}

export interface ValidationError {
  field: string;
  message: string;
  value?: any;
}

// Database query types
export interface QueryOptions {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  filters?: Record<string, any>;
}

export interface TenantContext {
  tenantId: string;
  tenant: Tenant;
}

// Express request extensions
export interface AuthenticatedRequest {
  user: JWTPayload;
  tenant: Tenant;
}
