import type { AppError } from '@virtualrealtour/types';

export class CustomError extends Error implements AppError {
  public readonly statusCode: number;
  public readonly isOperational: boolean;

  constructor(message: string, statusCode: number = 500, isOperational: boolean = true) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = isOperational;

    // Maintains proper stack trace for where our error was thrown
    Error.captureStackTrace(this, this.constructor);
  }
}

export class ValidationError extends CustomError {
  constructor(message: string) {
    super(message, 400);
  }
}

export class NotFoundError extends CustomError {
  constructor(message: string = 'Resource not found') {
    super(message, 404);
  }
}

export class UnauthorizedError extends CustomError {
  constructor(message: string = 'Unauthorized') {
    super(message, 401);
  }
}

export class ForbiddenError extends CustomError {
  constructor(message: string = 'Forbidden') {
    super(message, 403);
  }
}

export class ConflictError extends CustomError {
  constructor(message: string = 'Resource already exists') {
    super(message, 409);
  }
}

export class TenantNotFoundError extends NotFoundError {
  constructor(identifier: string) {
    super(`Tenant not found: ${identifier}`);
  }
}

export class StoreNotFoundError extends NotFoundError {
  constructor(identifier: string) {
    super(`Store not found: ${identifier}`);
  }
}

export class ProductNotFoundError extends NotFoundError {
  constructor(identifier: string) {
    super(`Product not found: ${identifier}`);
  }
}

export class WhatsAppError extends CustomError {
  constructor(message: string) {
    super(`WhatsApp API Error: ${message}`, 502);
  }
}

export class ReferralTrackingError extends CustomError {
  constructor(message: string) {
    super(`Referral Tracking Error: ${message}`, 500);
  }
}
