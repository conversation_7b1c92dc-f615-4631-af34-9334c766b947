import { prisma } from '@virtualrealtour/database';
import type { 
  Product, 
  ReferralAnalytics, 
  ReferralTrackingResult,
  StoreCommunicationConfig 
} from '@virtualrealtour/types';
import { ReferralTrackingError, ProductNotFoundError } from '@/utils/errors';
import logger from '@/utils/logger';
import { v4 as uuidv4 } from 'uuid';

export class ReferralService {
  /**
   * Generate a tracked referral URL for external website
   */
  async generateReferralUrl(
    productId: string,
    tenantId: string,
    utmSource: string = 'virtualrealtour',
    utmMedium: string = 'referral',
    utmCampaign?: string
  ): Promise<ReferralTrackingResult> {
    try {
      // Get product and store information
      const product = await prisma.product.findFirst({
        where: {
          id: productId,
          tenant_id: tenantId,
          status: 'active',
        },
        include: {
          store: true,
        },
      });

      if (!product) {
        throw new ProductNotFoundError(productId);
      }

      const storeConfig = product.store.communicationConfig as StoreCommunicationConfig;
      
      if (!storeConfig.external_website_enabled || !storeConfig.external_website_url) {
        throw new ReferralTrackingError('External website not configured for this store');
      }

      // Use product-specific URL or store's general URL
      const baseUrl = product.externalUrl || storeConfig.external_website_url;
      
      // Generate tracking parameters
      const trackingId = uuidv4();
      const campaign = utmCampaign || `product_${productId}`;
      
      const utmParams = new URLSearchParams({
        utm_source: utmSource,
        utm_medium: utmMedium,
        utm_campaign: campaign,
        utm_content: productId,
        vrt_tracking_id: trackingId,
      });

      // Create the tracked URL
      const separator = baseUrl.includes('?') ? '&' : '?';
      const referralUrl = `${baseUrl}${separator}${utmParams.toString()}`;

      // Create or update referral analytics record
      const analytics = await prisma.referralAnalytics.upsert({
        where: {
          productId_utmSource_utmMedium_utmCampaign: {
            productId: productId,
            utmSource: utmSource,
            utmMedium: utmMedium,
            utmCampaign: campaign,
          },
        },
        update: {
          referralUrl: referralUrl,
          updatedAt: new Date(),
        },
        create: {
          productId: productId,
          storeId: product.store_id,
          tenantId: tenantId,
          referralUrl: referralUrl,
          utmSource: utmSource,
          utmMedium: utmMedium,
          utmCampaign: campaign,
          clicks: 0,
          conversions: 0,
        },
      });

      logger.info(`Generated referral URL for product ${productId}`, {
        productId,
        storeId: product.store_id,
        tenantId,
        trackingId,
        referralUrl,
      });

      return {
        referralUrl,
        trackingId,
        analytics: {
          clicks: analytics.clicks,
          conversions: analytics.conversions,
        },
      };
    } catch (error) {
      logger.error('Failed to generate referral URL:', error);
      throw error instanceof ReferralTrackingError ? error : new ReferralTrackingError(error.message);
    }
  }

  /**
   * Track a referral click
   */
  async trackClick(
    productId: string,
    tenantId: string,
    utmSource: string,
    utmMedium: string,
    utmCampaign: string
  ): Promise<void> {
    try {
      await prisma.referralAnalytics.updateMany({
        where: {
          productId: productId,
          tenantId: tenantId,
          utmSource: utmSource,
          utmMedium: utmMedium,
          utmCampaign: utmCampaign,
        },
        data: {
          clicks: {
            increment: 1,
          },
          updatedAt: new Date(),
        },
      });

      logger.info(`Tracked referral click for product ${productId}`, {
        productId,
        tenantId,
        utmSource,
        utmMedium,
        utmCampaign,
      });
    } catch (error) {
      logger.error('Failed to track referral click:', error);
      throw new ReferralTrackingError(error.message);
    }
  }

  /**
   * Track a referral conversion
   */
  async trackConversion(
    productId: string,
    tenantId: string,
    utmSource: string,
    utmMedium: string,
    utmCampaign: string,
    revenue?: number
  ): Promise<void> {
    try {
      const updateData: any = {
        conversions: {
          increment: 1,
        },
        updatedAt: new Date(),
      };

      if (revenue !== undefined) {
        updateData.revenueTracked = {
          increment: revenue,
        };
      }

      await prisma.referralAnalytics.updateMany({
        where: {
          productId: productId,
          tenantId: tenantId,
          utmSource: utmSource,
          utmMedium: utmMedium,
          utmCampaign: utmCampaign,
        },
        data: updateData,
      });

      logger.info(`Tracked referral conversion for product ${productId}`, {
        productId,
        tenantId,
        utmSource,
        utmMedium,
        utmCampaign,
        revenue,
      });
    } catch (error) {
      logger.error('Failed to track referral conversion:', error);
      throw new ReferralTrackingError(error.message);
    }
  }

  /**
   * Get referral analytics for a product
   */
  async getProductReferralAnalytics(
    productId: string,
    tenantId: string
  ): Promise<ReferralAnalytics[]> {
    try {
      const analytics = await prisma.referralAnalytics.findMany({
        where: {
          productId: productId,
          tenantId: tenantId,
        },
        orderBy: {
          createdAt: 'desc',
        },
      });

      return analytics as ReferralAnalytics[];
    } catch (error) {
      logger.error('Failed to get referral analytics:', error);
      throw new ReferralTrackingError(error.message);
    }
  }

  /**
   * Get referral analytics for a store
   */
  async getStoreReferralAnalytics(
    storeId: string,
    tenantId: string
  ): Promise<ReferralAnalytics[]> {
    try {
      const analytics = await prisma.referralAnalytics.findMany({
        where: {
          storeId: storeId,
          tenantId: tenantId,
        },
        include: {
          product: {
            select: {
              name: true,
              price: true,
              currency: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
      });

      return analytics as ReferralAnalytics[];
    } catch (error) {
      logger.error('Failed to get store referral analytics:', error);
      throw new ReferralTrackingError(error.message);
    }
  }

  /**
   * Get aggregated referral statistics
   */
  async getReferralStats(tenantId: string, storeId?: string) {
    try {
      const whereClause: any = { tenantId };
      if (storeId) {
        whereClause.storeId = storeId;
      }

      const stats = await prisma.referralAnalytics.aggregate({
        where: whereClause,
        _sum: {
          clicks: true,
          conversions: true,
          revenueTracked: true,
        },
        _count: {
          id: true,
        },
      });

      const conversionRate = stats._sum.clicks > 0 
        ? (stats._sum.conversions / stats._sum.clicks) * 100 
        : 0;

      return {
        totalReferrals: stats._count.id,
        totalClicks: stats._sum.clicks || 0,
        totalConversions: stats._sum.conversions || 0,
        totalRevenue: stats._sum.revenueTracked || 0,
        conversionRate: Math.round(conversionRate * 100) / 100,
      };
    } catch (error) {
      logger.error('Failed to get referral stats:', error);
      throw new ReferralTrackingError(error.message);
    }
  }
}

export default ReferralService;
