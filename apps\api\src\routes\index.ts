import { Router } from 'express';
import { Request, Response } from 'express';
import ResponseHelper from '../utils/response.js';
import productRoutes from './products.js';
import storeRoutes from './stores.js';

const router = Router();

/**
 * Health check endpoint
 */
router.get('/health', (req: Request, res: Response) => {
  return ResponseHelper.success(res, {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    environment: process.env.NODE_ENV || 'development',
  }, 'API is healthy');
});

/**
 * API info endpoint
 */
router.get('/', (req: Request, res: Response) => {
  return ResponseHelper.success(res, {
    name: 'VirtualRealTour API',
    description: 'Product Discovery and Referral Platform API',
    version: '1.0.0',
    documentation: '/api/docs',
    endpoints: {
      stores: '/api/stores',
      products: '/api/products',
      health: '/api/health',
    },
  }, 'Welcome to VirtualRealTour API');
});

// Mount route modules
router.use('/stores', storeRoutes);
router.use('/products', productRoutes);

export default router;
