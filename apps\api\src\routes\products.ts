import { Router } from 'express';
import ProductController from '../controllers/ProductController.js';
import { requireTenant } from '../middleware/tenant.js';
import { optionalAuth } from '../middleware/auth.js';
import {
  validateProductId,
  validateWhatsAppInquiry,
  validateExternalReferral,
  validatePagination,
} from '../middleware/validation.js';

const router = Router();
const productController = new ProductController();

// Apply tenant middleware to all routes
router.use(requireTenant);

/**
 * @route GET /api/products/search
 * @desc Search products across all stores
 * @access Public (with optional auth)
 */
router.get(
  '/search',
  optionalAuth,
  validatePagination,
  productController.searchProducts
);

/**
 * @route GET /api/products/:productId
 * @desc Get a single product
 * @access Public (with optional auth)
 */
router.get(
  '/:productId',
  validateProductId,
  optionalAuth,
  productController.getProduct
);

/**
 * @route POST /api/products/:productId/whatsapp-inquiry
 * @desc Send WhatsApp inquiry for a product
 * @access Public
 */
router.post(
  '/:productId/whatsapp-inquiry',
  validateProductId,
  validateWhatsAppInquiry,
  productController.sendWhatsAppInquiry
);

/**
 * @route POST /api/products/:productId/external-referral
 * @desc Generate external referral URL for a product
 * @access Public
 */
router.post(
  '/:productId/external-referral',
  validateProductId,
  validateExternalReferral,
  productController.generateExternalReferral
);

/**
 * @route GET /api/products/:productId/track-click
 * @desc Track referral click for a product
 * @access Public
 */
router.get(
  '/:productId/track-click',
  validateProductId,
  productController.trackReferralClick
);

/**
 * @route GET /api/products/:productId/analytics
 * @desc Get product referral analytics
 * @access Public (with optional auth)
 */
router.get(
  '/:productId/analytics',
  validateProductId,
  optionalAuth,
  productController.getProductAnalytics
);

export default router;
