'use client';

import { motion } from 'framer-motion';
import { 
  EyeIcon, 
  CubeIcon, 
  CameraIcon,
  DevicePhoneMobileIcon,
  GlobeAltIcon,
  SparklesIcon,
  BuildingOfficeIcon,
  AcademicCapIcon
} from '@heroicons/react/24/outline';
import Link from 'next/link';

export function ServicesSection() {
  const services = [
    {
      icon: EyeIcon,
      title: '360 Virtual Tours',
      description: 'Immersive virtual experiences that allow customers to explore your space from anywhere in the world.',
      features: ['8K Ultra HD Quality', 'Interactive Hotspots', 'Mobile Optimized', 'VR Compatible'],
      color: 'from-blue-500 to-cyan-500',
      href: '/services/360-tours'
    },
    {
      icon: CubeIcon,
      title: '3D Virtual Worlds',
      description: 'Create stunning 3D environments and interactive virtual spaces for your business.',
      features: ['Photorealistic Rendering', 'Interactive Elements', 'Multi-Platform', 'Custom Branding'],
      color: 'from-purple-500 to-pink-500',
      href: '/services/3d-worlds'
    },
    {
      icon: SparklesIcon,
      title: 'Virtual Staging',
      description: 'Transform empty spaces into beautifully furnished environments using cutting-edge technology.',
      features: ['Realistic Furniture', 'Multiple Styles', 'Cost Effective', 'Quick Turnaround'],
      color: 'from-green-500 to-emerald-500',
      href: '/services/virtual-staging'
    },
    {
      icon: AcademicCapIcon,
      title: 'V-Learning & Gamification',
      description: 'Educational virtual experiences with interactive learning modules and gamified elements.',
      features: ['Interactive Learning', 'Progress Tracking', 'Engaging Content', 'Multi-Device'],
      color: 'from-orange-500 to-red-500',
      href: '/services/v-learning'
    },
    {
      icon: CameraIcon,
      title: '8K 360 Shooting',
      description: 'Professional 360-degree photography and videography services with ultra-high definition quality.',
      features: ['8K Resolution', 'Professional Equipment', 'Expert Team', 'Post-Processing'],
      color: 'from-indigo-500 to-blue-500',
      href: '/services/8k-shooting'
    },
    {
      icon: DevicePhoneMobileIcon,
      title: 'Augmented Reality',
      description: 'Blend digital content with the real world through innovative AR experiences.',
      features: ['Mobile AR', 'Web AR', 'Interactive Objects', 'Brand Integration'],
      color: 'from-teal-500 to-cyan-500',
      href: '/services/augmented-reality'
    },
    {
      icon: GlobeAltIcon,
      title: 'Interactive 3D Models',
      description: 'Detailed 3D models that customers can interact with, rotate, and explore in detail.',
      features: ['High Detail', 'Interactive Controls', 'Web Optimized', 'Mobile Friendly'],
      color: 'from-pink-500 to-rose-500',
      href: '/services/3d-models'
    },
    {
      icon: BuildingOfficeIcon,
      title: 'AEC Site Suite',
      description: 'Comprehensive virtual solutions for Architecture, Engineering, and Construction projects.',
      features: ['Project Visualization', 'Progress Tracking', 'Collaboration Tools', 'Documentation'],
      color: 'from-yellow-500 to-orange-500',
      href: '/services/aec-suite'
    }
  ];

  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 mb-6">
            Our <span className="text-blue-600">Services</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
            We offer a comprehensive suite of virtual reality and immersive technology services 
            to transform how businesses showcase their products and spaces across Nigeria.
          </p>
        </motion.div>

        {/* Services Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {services.map((service, index) => (
            <motion.div
              key={service.title}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="group"
            >
              <Link href={service.href} className="block">
                <div className="bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100 overflow-hidden">
                  {/* Service Icon */}
                  <div className={`h-32 bg-gradient-to-br ${service.color} flex items-center justify-center relative overflow-hidden`}>
                    <service.icon className="w-12 h-12 text-white z-10" />
                    <div className="absolute inset-0 bg-black/10 group-hover:bg-black/20 transition-colors duration-300" />
                  </div>

                  {/* Service Content */}
                  <div className="p-6">
                    <h3 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors">
                      {service.title}
                    </h3>
                    <p className="text-gray-600 mb-4 text-sm leading-relaxed">
                      {service.description}
                    </p>

                    {/* Features List */}
                    <ul className="space-y-2 mb-4">
                      {service.features.map((feature, idx) => (
                        <li key={idx} className="flex items-center text-sm text-gray-500">
                          <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mr-2 flex-shrink-0" />
                          {feature}
                        </li>
                      ))}
                    </ul>

                    {/* Learn More Link */}
                    <div className="text-blue-600 font-semibold text-sm group-hover:text-blue-700 transition-colors">
                      Learn More →
                    </div>
                  </div>
                </div>
              </Link>
            </motion.div>
          ))}
        </div>

        {/* CTA Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-8 text-white">
            <h3 className="text-2xl sm:text-3xl font-bold mb-4">
              Ready to Transform Your Business?
            </h3>
            <p className="text-lg mb-6 text-blue-100">
              Let&apos;s create an immersive virtual experience that sets you apart from the competition.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link 
                href="/contact" 
                className="bg-white text-blue-600 hover:bg-gray-100 px-8 py-3 rounded-lg font-semibold transition-colors"
              >
                Get Started Today
              </Link>
              <Link 
                href="/portfolio" 
                className="border-2 border-white text-white hover:bg-white hover:text-blue-600 px-8 py-3 rounded-lg font-semibold transition-colors"
              >
                View Our Work
              </Link>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
