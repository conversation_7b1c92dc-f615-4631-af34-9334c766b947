import { Router } from 'express';
import StoreController from '@/controllers/StoreController';
import ProductController from '@/controllers/ProductController';
import { requireTenant } from '@/middleware/tenant';
import { optionalAuth, requireRole } from '@/middleware/auth';
import {
  validateStoreId,
  validatePagination,
  validateStoreConfig,
} from '@/middleware/validation';

const router = Router();
const storeController = new StoreController();
const productController = new ProductController();

// Apply tenant middleware to all routes
router.use(requireTenant);

/**
 * @route GET /api/stores
 * @desc Get all stores for a tenant
 * @access Public (with optional auth)
 */
router.get(
  '/',
  optionalAuth,
  validatePagination,
  storeController.getStores
);

/**
 * @route GET /api/stores/:storeId
 * @desc Get a single store
 * @access Public (with optional auth)
 */
router.get(
  '/:storeId',
  validateStoreId,
  optionalAuth,
  storeController.getStore
);

/**
 * @route GET /api/stores/:storeId/products
 * @desc Get products for a store
 * @access Public (with optional auth)
 */
router.get(
  '/:storeId/products',
  validateStoreId,
  optionalAuth,
  validatePagination,
  productController.getStoreProducts
);

/**
 * @route GET /api/stores/:storeId/config
 * @desc Get store communication configuration
 * @access Store Owner, Admin
 */
router.get(
  '/:storeId/config',
  validateStoreId,
  requireRole('store_owner', 'tenant_admin', 'super_admin'),
  storeController.getStoreConfig
);

/**
 * @route PUT /api/stores/:storeId/config
 * @desc Update store communication configuration
 * @access Store Owner, Admin
 */
router.put(
  '/:storeId/config',
  validateStoreId,
  requireRole('store_owner', 'tenant_admin', 'super_admin'),
  validateStoreConfig,
  storeController.updateStoreConfig
);

/**
 * @route GET /api/stores/:storeId/inquiries
 * @desc Get inquiries for a store
 * @access Store Owner, Admin
 */
router.get(
  '/:storeId/inquiries',
  validateStoreId,
  requireRole('store_owner', 'tenant_admin', 'super_admin'),
  validatePagination,
  storeController.getStoreInquiries
);

/**
 * @route GET /api/stores/:storeId/analytics
 * @desc Get store analytics
 * @access Store Owner, Admin
 */
router.get(
  '/:storeId/analytics',
  validateStoreId,
  requireRole('store_owner', 'tenant_admin', 'super_admin'),
  storeController.getStoreAnalytics
);

/**
 * @route GET /api/stores/:storeId/referral-analytics
 * @desc Get store referral analytics
 * @access Store Owner, Admin
 */
router.get(
  '/:storeId/referral-analytics',
  validateStoreId,
  requireRole('store_owner', 'tenant_admin', 'super_admin'),
  storeController.getStoreReferralAnalytics
);

export default router;
