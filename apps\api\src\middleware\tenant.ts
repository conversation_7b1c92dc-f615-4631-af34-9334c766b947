import { Request, Response, NextFunction } from 'express';
import { prisma } from '../../../packages/database/dist/index';
import type { Tenant } from '../../../packages/types/dist/index';
import { TenantNotFoundError } from '@/utils/errors';
import logger from '@/utils/logger';

// Extend Express Request to include tenant
declare global {
  namespace Express {
    interface Request {
      tenant?: Tenant;
    }
  }
}

/**
 * Middleware to detect and validate tenant from subdomain or domain
 */
export async function tenantMiddleware(req: Request, res: Response, next: NextFunction) {
  try {
    const host = req.headers.host;

    if (!host) {
      throw new TenantNotFoundError('No host header provided');
    }

    // Extract subdomain or use full domain
    let identifier: string;

    // Check if it's a subdomain (e.g., demo.virtualrealtour.com)
    const parts = host.split('.');
    if (parts.length > 2) {
      identifier = parts[0]; // Use subdomain
    } else {
      identifier = host; // Use full domain
    }

    // Remove port if present (for development)
    identifier = identifier.split(':')[0];

    logger.info(`Tenant detection for identifier: ${identifier}`);

    // Find tenant by subdomain or domain
    const tenant = await prisma.tenant.findFirst({
      where: {
        OR: [
          { subdomain: identifier },
          { domain: host },
        ],
        status: 'active',
      },
    });

    if (!tenant) {
      throw new TenantNotFoundError(identifier);
    }

    // Attach tenant to request
    req.tenant = tenant as Tenant;

    logger.info(`Tenant found: ${tenant.name} (${tenant.id})`);

    next();
  } catch (error) {
    logger.error('Tenant middleware error:', error);
    next(error);
  }
}

/**
 * Middleware to ensure tenant context is available
 */
export function requireTenant(req: Request, res: Response, next: NextFunction) {
  if (!req.tenant) {
    throw new TenantNotFoundError('Tenant context not available');
  }
  next();
}

export default tenantMiddleware;
