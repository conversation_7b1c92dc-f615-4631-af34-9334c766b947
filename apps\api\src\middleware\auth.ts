import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import type { JWTPayload, UserRole } from '@/types';
import { UnauthorizedError, ForbiddenError } from '@/utils/errors';
import config from '@/config';
import logger from '@/utils/logger';

// Extend Express Request to include user
declare global {
  namespace Express {
    interface Request {
      user?: JWTPayload;
    }
  }
}

/**
 * Middleware to authenticate JWT token
 */
export function authenticateToken(req: Request, res: Response, next: NextFunction) {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      throw new UnauthorizedError('Access token required');
    }

    const decoded = jwt.verify(token, config.jwt.secret) as JWTPayload;

    // Verify tenant context matches token
    if (req.tenant && decoded.tenantId !== req.tenant.id) {
      throw new ForbiddenError('Token tenant mismatch');
    }

    req.user = decoded;
    logger.info(`User authenticated: ${decoded.userId} (${decoded.role})`);

    next();
  } catch (error) {
    if (error instanceof jwt.JsonWebTokenError) {
      logger.error('JWT verification failed:', error.message);
      next(new UnauthorizedError('Invalid access token'));
    } else {
      next(error);
    }
  }
}

/**
 * Middleware to check if user has required role
 */
export function requireRole(...roles: UserRole[]) {
  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      throw new UnauthorizedError('Authentication required');
    }

    if (!roles.includes(req.user.role)) {
      throw new ForbiddenError(`Required role: ${roles.join(' or ')}`);
    }

    next();
  };
}

/**
 * Middleware to check if user has required permission
 */
export function requirePermission(...permissions: string[]) {
  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      throw new UnauthorizedError('Authentication required');
    }

    const userPermissions = req.user.permissions || [];
    const hasPermission = permissions.some(permission =>
      userPermissions.includes(permission)
    );

    if (!hasPermission) {
      throw new ForbiddenError(`Required permission: ${permissions.join(' or ')}`);
    }

    next();
  };
}

/**
 * Optional authentication - doesn't throw error if no token
 */
export function optionalAuth(req: Request, res: Response, next: NextFunction) {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1];

    if (token) {
      const decoded = jwt.verify(token, config.jwt.secret) as JWTPayload;
      req.user = decoded;
    }

    next();
  } catch (error) {
    // Ignore authentication errors for optional auth
    logger.warn('Optional auth failed:', error);
    next();
  }
}

/**
 * Generate JWT token
 */
export function generateToken(payload: Omit<JWTPayload, 'iat' | 'exp'>): string {
  const tokenPayload = {
    userId: payload.userId,
    tenantId: payload.tenantId,
    role: payload.role,
    permissions: payload.permissions,
  };

  return jwt.sign(
    tokenPayload,
    config.jwt.secret as string,
    {
      expiresIn: config.jwt.expiresIn,
    }
  );
}

export default authenticateToken;
