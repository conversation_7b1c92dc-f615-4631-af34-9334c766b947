import Link from 'next/link';
import Image from 'next/image';
import { 
  PlayIcon, 
  EyeIcon, 
  ClockIcon,
  StarIcon,
  MapPinIcon
} from '@heroicons/react/24/outline';
import { Tour } from '@/types';
import { motion } from 'framer-motion';

interface TourCardProps {
  tour: Tour;
  className?: string;
}

export function TourCard({ tour, className = '' }: TourCardProps) {
  const getTourTypeIcon = (type: string) => {
    switch (type) {
      case '3d':
        return <EyeIcon className="w-4 h-4" />;
      case 'video':
        return <PlayIcon className="w-4 h-4" />;
      case 'panorama':
        return <EyeIcon className="w-4 h-4" />;
      default:
        return <EyeIcon className="w-4 h-4" />;
    }
  };

  const getTourTypeLabel = (type: string) => {
    switch (type) {
      case '3d':
        return '3D Tour';
      case 'video':
        return '360° Video';
      case 'panorama':
        return 'Panorama';
      case 'embed':
        return 'Interactive';
      default:
        return 'Virtual Tour';
    }
  };

  const getDifficultyColor = (difficulty?: string) => {
    switch (difficulty) {
      case 'easy':
        return 'bg-green-100 text-green-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'advanced':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Generate a placeholder image URL based on tour type
  const getPlaceholderImage = (type: string) => {
    const baseUrl = 'https://images.unsplash.com';
    switch (type) {
      case '3d':
        return `${baseUrl}/photo-1560472354-b33ff0c44a43?w=400&h=250&fit=crop`;
      case 'video':
        return `${baseUrl}/photo-1441986300917-64674bd600d8?w=400&h=250&fit=crop`;
      case 'panorama':
        return `${baseUrl}/photo-1586023492125-27b2c045efd7?w=400&h=250&fit=crop`;
      default:
        return `${baseUrl}/photo-1518709268805-4e9042af2176?w=400&h=250&fit=crop`;
    }
  };

  return (
    <motion.div
      whileHover={{ y: -4 }}
      transition={{ duration: 0.2 }}
      className={`card-hover group ${className}`}
    >
      {/* Tour Image */}
      <div className="relative aspect-video overflow-hidden">
        <Image
          src={getPlaceholderImage(tour.tour_type)}
          alt={tour.title}
          fill
          className="object-cover group-hover:scale-105 transition-transform duration-300"
        />
        
        {/* Overlay */}
        <div className="absolute inset-0 bg-black/20 group-hover:bg-black/30 transition-colors duration-300" />
        
        {/* Tour Type Badge */}
        <div className="absolute top-3 left-3">
          <div className="flex items-center space-x-1 bg-white/90 backdrop-blur-sm px-2 py-1 rounded-full text-xs font-medium">
            {getTourTypeIcon(tour.tour_type)}
            <span>{getTourTypeLabel(tour.tour_type)}</span>
          </div>
        </div>

        {/* Featured Badge */}
        {tour.metadata?.featured && (
          <div className="absolute top-3 right-3">
            <div className="flex items-center space-x-1 bg-yellow-400 text-yellow-900 px-2 py-1 rounded-full text-xs font-medium">
              <StarIcon className="w-3 h-3" />
              <span>Featured</span>
            </div>
          </div>
        )}

        {/* Play Button Overlay */}
        <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <div className="w-16 h-16 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center">
            <PlayIcon className="w-8 h-8 text-gray-900 ml-1" />
          </div>
        </div>
      </div>

      {/* Tour Content */}
      <div className="p-6">
        {/* Store Info */}
        {tour.store && (
          <div className="flex items-center space-x-2 text-sm text-gray-600 mb-2">
            <MapPinIcon className="w-4 h-4" />
            <span>{tour.store.name}</span>
          </div>
        )}

        {/* Title */}
        <h3 className="text-xl font-semibold text-gray-900 mb-2 group-hover:text-black transition-colors">
          {tour.title}
        </h3>

        {/* Description */}
        <p className="text-gray-600 text-sm mb-4 line-clamp-2">
          {tour.description || 'Experience this immersive virtual tour and discover amazing products.'}
        </p>

        {/* Tour Metadata */}
        <div className="flex items-center justify-between mb-4">
          {/* Duration */}
          {tour.metadata?.duration && (
            <div className="flex items-center space-x-1 text-sm text-gray-500">
              <ClockIcon className="w-4 h-4" />
              <span>{tour.metadata.duration}</span>
            </div>
          )}

          {/* Difficulty */}
          {tour.metadata?.difficulty && (
            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(tour.metadata.difficulty)}`}>
              {tour.metadata.difficulty}
            </span>
          )}
        </div>

        {/* Hotspots Count */}
        {tour.hotspots && tour.hotspots.length > 0 && (
          <div className="text-sm text-gray-500 mb-4">
            {tour.hotspots.length} interactive {tour.hotspots.length === 1 ? 'hotspot' : 'hotspots'}
          </div>
        )}

        {/* Action Button */}
        <Link
          href={`/tours/${tour.id}`}
          className="btn-primary w-full text-center group-hover:bg-gray-800 transition-colors"
        >
          Start Virtual Tour
        </Link>
      </div>
    </motion.div>
  );
}
