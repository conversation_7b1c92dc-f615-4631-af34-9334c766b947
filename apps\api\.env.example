# Database
DATABASE_URL="postgresql://username:password@localhost:5432/virtualrealtour"

# Server
PORT=3001
NODE_ENV=development

# Authentication
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRY=24h

# CORS
CORS_ORIGIN=http://localhost:3000

# WhatsApp Business API
WHATSAPP_BUSINESS_API_URL=https://graph.facebook.com/v17.0
WHATSAPP_BUSINESS_API_TOKEN=your-whatsapp-business-api-token

# Logging
LOG_LEVEL=info

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# External Services
ANALYTICS_WEBHOOK_URL=https://your-analytics-service.com/webhook
