@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    scroll-behavior: smooth;
  }
  
  body {
    @apply font-sans;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-gray-100;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-gray-300 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400;
  }

  /* Focus styles */
  *:focus {
    @apply outline-none ring-2 ring-blue-500 ring-offset-2;
  }

  /* Selection styles */
  ::selection {
    @apply bg-blue-100 text-blue-900;
  }
}

@layer components {
  /* Button variants */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .btn-primary {
    @apply btn bg-black text-white hover:bg-gray-800 focus:ring-black;
  }

  .btn-secondary {
    @apply btn bg-white text-gray-900 border border-gray-300 hover:bg-gray-50 focus:ring-gray-500;
  }

  .btn-ghost {
    @apply btn bg-transparent text-gray-700 hover:bg-gray-100 focus:ring-gray-500;
  }

  .btn-danger {
    @apply btn bg-red-600 text-white hover:bg-red-700 focus:ring-red-500;
  }

  /* Input styles */
  .input {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent sm:text-sm;
  }

  .input-error {
    @apply input border-red-300 focus:ring-red-500;
  }

  /* Card styles */
  .card {
    @apply bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden;
  }

  .card-hover {
    @apply card transition-all duration-200 hover:shadow-md hover:border-gray-300;
  }

  /* Navigation styles */
  .nav-link {
    @apply text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200;
  }

  .nav-link-active {
    @apply nav-link text-gray-900 bg-gray-100;
  }

  /* Badge styles */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }

  .badge-success {
    @apply badge bg-green-100 text-green-800;
  }

  .badge-warning {
    @apply badge bg-yellow-100 text-yellow-800;
  }

  .badge-error {
    @apply badge bg-red-100 text-red-800;
  }

  .badge-info {
    @apply badge bg-blue-100 text-blue-800;
  }

  /* Loading spinner */
  .spinner {
    @apply animate-spin rounded-full border-2 border-gray-300 border-t-gray-900;
  }

  /* Gradient backgrounds */
  .gradient-bg {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }

  .gradient-text {
    @apply bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent;
  }

  /* Tour viewer styles */
  .tour-viewer {
    @apply relative w-full h-full bg-black rounded-lg overflow-hidden;
  }

  .tour-hotspot {
    @apply absolute w-6 h-6 bg-white rounded-full border-2 border-black cursor-pointer transform -translate-x-1/2 -translate-y-1/2 hover:scale-110 transition-transform duration-200;
  }

  .tour-hotspot::before {
    content: '';
    @apply absolute inset-0 bg-white rounded-full animate-ping;
  }

  /* Map styles */
  .map-container {
    @apply relative w-full h-full rounded-lg overflow-hidden;
  }

  .map-marker {
    @apply w-8 h-8 bg-black rounded-full border-2 border-white shadow-lg cursor-pointer hover:scale-110 transition-transform duration-200;
  }

  /* Dashboard styles */
  .dashboard-card {
    @apply card p-6;
  }

  .dashboard-stat {
    @apply text-center;
  }

  .dashboard-stat-value {
    @apply text-3xl font-bold text-gray-900;
  }

  .dashboard-stat-label {
    @apply text-sm text-gray-600 mt-1;
  }

  /* Mobile responsive utilities */
  .mobile-hidden {
    @apply hidden md:block;
  }

  .mobile-only {
    @apply block md:hidden;
  }

  /* Animation utilities */
  .fade-in {
    @apply animate-fade-in;
  }

  .slide-up {
    @apply animate-slide-up;
  }

  .scale-in {
    @apply animate-scale-in;
  }

  /* Custom grid layouts */
  .grid-auto-fit {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  }

  .grid-auto-fill {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  }
}

@layer utilities {
  /* Text utilities */
  .text-balance {
    text-wrap: balance;
  }

  /* Aspect ratio utilities */
  .aspect-video {
    aspect-ratio: 16 / 9;
  }

  .aspect-square {
    aspect-ratio: 1 / 1;
  }

  .aspect-tour {
    aspect-ratio: 2 / 1;
  }

  /* Backdrop utilities */
  .backdrop-blur-xs {
    backdrop-filter: blur(2px);
  }

  /* Safe area utilities for mobile */
  .safe-top {
    padding-top: env(safe-area-inset-top);
  }

  .safe-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }

  /* Print utilities */
  @media print {
    .print-hidden {
      display: none !important;
    }
  }
}
