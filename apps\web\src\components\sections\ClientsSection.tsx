'use client';

import { motion } from 'framer-motion';

export function ClientsSection() {
  const clients = [
    { name: 'Shoprite Nigeria', logo: 'https://via.placeholder.com/150x80/000000/FFFFFF?text=Shoprite' },
    { name: 'Transcorp Hilton', logo: 'https://via.placeholder.com/150x80/000000/FFFFFF?text=Hilton' },
    { name: 'University of Lagos', logo: 'https://via.placeholder.com/150x80/000000/FFFFFF?text=UNILAG' },
    { name: 'Landmark Group', logo: 'https://via.placeholder.com/150x80/000000/FFFFFF?text=Landmark' },
    { name: 'Nigerian Breweries', logo: 'https://via.placeholder.com/150x80/000000/FFFFFF?text=NB' },
    { name: 'Access Bank', logo: 'https://via.placeholder.com/150x80/000000/FFFFFF?text=Access' },
    { name: 'Dangote Group', logo: 'https://via.placeholder.com/150x80/000000/FFFFFF?text=Dangote' },
    { name: 'MTN Nigeria', logo: 'https://via.placeholder.com/150x80/000000/FFFFFF?text=MTN' }
  ];

  return (
    <section className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl sm:text-5xl font-bold text-gray-900 mb-6">
            Trusted by Leading <span className="text-blue-600">Nigerian Brands</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            We&apos;ve had the privilege of working with some of Nigeria&apos;s most respected companies and institutions.
          </p>
        </motion.div>

        <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
          {clients.map((client, index) => (
            <motion.div
              key={client.name}
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="flex items-center justify-center p-6 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300"
            >
              <img 
                src={client.logo} 
                alt={client.name}
                className="max-h-12 w-auto opacity-60 hover:opacity-100 transition-opacity duration-300"
              />
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}
