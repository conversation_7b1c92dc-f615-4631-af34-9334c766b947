import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables');
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Database types based on our schema
export interface Database {
  public: {
    Tables: {
      tenants: {
        Row: {
          id: string;
          name: string;
          subdomain: string;
          domain: string | null;
          config: any;
          status: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          subdomain: string;
          domain?: string | null;
          config: any;
          status?: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          subdomain?: string;
          domain?: string | null;
          config?: any;
          status?: string;
          created_at?: string;
          updated_at?: string;
        };
      };
      stores: {
        Row: {
          id: string;
          tenant_id: string;
          name: string;
          description: string | null;
          slug: string;
          logo_url: string | null;
          banner_url: string | null;
          status: string;
          communication_config: any;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          tenant_id: string;
          name: string;
          description?: string | null;
          slug: string;
          logo_url?: string | null;
          banner_url?: string | null;
          status?: string;
          communication_config: any;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          tenant_id?: string;
          name?: string;
          description?: string | null;
          slug?: string;
          logo_url?: string | null;
          banner_url?: string | null;
          status?: string;
          communication_config?: any;
          created_at?: string;
          updated_at?: string;
        };
      };
      products: {
        Row: {
          id: string;
          store_id: string;
          tenant_id: string;
          name: string;
          description: string | null;
          price: number;
          currency: string;
          images: string[];
          has_3d_model: boolean;
          vr_ready: boolean;
          video_360_url: string | null;
          external_url: string | null;
          whatsapp_inquiry_enabled: boolean;
          referral_tracking_enabled: boolean;
          metadata: any;
          status: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          store_id: string;
          tenant_id: string;
          name: string;
          description?: string | null;
          price: number;
          currency?: string;
          images: string[];
          has_3d_model?: boolean;
          vr_ready?: boolean;
          video_360_url?: string | null;
          external_url?: string | null;
          whatsapp_inquiry_enabled?: boolean;
          referral_tracking_enabled?: boolean;
          metadata?: any;
          status?: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          store_id?: string;
          tenant_id?: string;
          name?: string;
          description?: string | null;
          price?: number;
          currency?: string;
          images?: string[];
          has_3d_model?: boolean;
          vr_ready?: boolean;
          video_360_url?: string | null;
          external_url?: string | null;
          whatsapp_inquiry_enabled?: boolean;
          referral_tracking_enabled?: boolean;
          metadata?: any;
          status?: string;
          created_at?: string;
          updated_at?: string;
        };
      };
      tours: {
        Row: {
          id: string;
          tenant_id: string;
          store_id: string;
          title: string;
          description: string | null;
          tour_type: string;
          tour_url: string;
          hotspots: any;
          metadata: any;
          status: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          tenant_id: string;
          store_id: string;
          title: string;
          description?: string | null;
          tour_type: string;
          tour_url: string;
          hotspots?: any;
          metadata?: any;
          status?: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          tenant_id?: string;
          store_id?: string;
          title?: string;
          description?: string | null;
          tour_type?: string;
          tour_url?: string;
          hotspots?: any;
          metadata?: any;
          status?: string;
          created_at?: string;
          updated_at?: string;
        };
      };
      product_inquiries: {
        Row: {
          id: string;
          product_id: string;
          store_id: string;
          tenant_id: string;
          customer_name: string;
          customer_email: string;
          customer_phone: string | null;
          inquiry_type: string;
          message: string;
          status: string;
          whatsapp_message_id: string | null;
          referral_url: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          product_id: string;
          store_id: string;
          tenant_id: string;
          customer_name: string;
          customer_email: string;
          customer_phone?: string | null;
          inquiry_type: string;
          message: string;
          status?: string;
          whatsapp_message_id?: string | null;
          referral_url?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          product_id?: string;
          store_id?: string;
          tenant_id?: string;
          customer_name?: string;
          customer_email?: string;
          customer_phone?: string | null;
          inquiry_type?: string;
          message?: string;
          status?: string;
          whatsapp_message_id?: string | null;
          referral_url?: string | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      referral_analytics: {
        Row: {
          id: string;
          product_id: string;
          store_id: string;
          tenant_id: string;
          referral_url: string;
          clicks: number;
          conversions: number;
          revenue_tracked: number | null;
          utm_source: string;
          utm_medium: string;
          utm_campaign: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          product_id: string;
          store_id: string;
          tenant_id: string;
          referral_url: string;
          clicks?: number;
          conversions?: number;
          revenue_tracked?: number | null;
          utm_source: string;
          utm_medium: string;
          utm_campaign: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          product_id?: string;
          store_id?: string;
          tenant_id?: string;
          referral_url?: string;
          clicks?: number;
          conversions?: number;
          revenue_tracked?: number | null;
          utm_source?: string;
          utm_medium?: string;
          utm_campaign?: string;
          created_at?: string;
          updated_at?: string;
        };
      };
    };
  };
}

export type Tables<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Row'];
export type Inserts<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Insert'];
export type Updates<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Update'];
