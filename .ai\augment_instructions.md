# Augment Code AI Instructions - VirtualRealTour

## Project Context
You are working on VirtualRealTour, a white-label virtual product discovery and referral platform built with Next.js 14, Express.js, and a Turborepo monorepo structure. The platform functions as a virtual showroom that connects customers with store owners through WhatsApp integration and external website referrals. Your role is to handle code integration, conversion, and backend development.

## Development Guidelines

### Code Standards
- **TypeScript First**: All code must be written in TypeScript with strict type checking
- **ESLint + Prettier**: Follow the established linting and formatting rules
- **Testing**: Write unit tests for all utility functions and integration tests for APIs
- **Documentation**: Include JSDoc comments for all public functions and complex logic

### Architecture Principles
- **Separation of Concerns**: Keep business logic separate from UI components
- **Dependency Injection**: Use dependency injection for services and data access
- **Error Handling**: Implement comprehensive error handling with proper logging
- **Performance**: Optimize for server-side rendering and client-side hydration

## Core Responsibilities

### 1. Lovable.dev Integration
Convert React components from lovable.dev (Vite) to Next.js 14 compatible format:

```typescript
// Convert from Vite routing
import { useNavigate } from 'react-router-dom';
// To Next.js routing
import { useRouter } from 'next/navigation';

// Convert from Vite imports
import Component from './Component';
// To Next.js imports with proper typing
import { Component } from '@/components/ui/Component';
```

#### Component Conversion Checklist
- [ ] Replace React Router with Next.js router
- [ ] Convert CSS imports to Tailwind classes
- [ ] Ensure SSR compatibility (no window/document access)
- [ ] Add TypeScript interfaces for props
- [ ] Implement error boundaries
- [ ] Add loading states and skeletons

### 2. Backend Development (Express.js)

#### Multi-Tenant Architecture
```typescript
// Tenant middleware example
export async function tenantMiddleware(req: Request, res: Response, next: NextFunction) {
  const subdomain = req.headers.host?.split('.')[0];
  const tenant = await getTenantBySubdomain(subdomain);

  if (!tenant) {
    return res.status(404).json({ error: 'Tenant not found' });
  }

  req.tenant = tenant;
  next();
}
```

#### Custom Services
```typescript
// Tour service example
export class TourService {
  async createTour(tenantId: string, tourData: CreateTourDTO): Promise<Tour> {
    // Implementation with tenant isolation
  }

  async getToursByStore(storeId: string, tenantId: string): Promise<Tour[]> {
    // Implementation with proper filtering
  }
}

// WhatsApp service example
export class WhatsAppService {
  async sendProductInquiry(
    storeWhatsApp: string,
    productInfo: ProductInfo,
    customerMessage: string
  ): Promise<WhatsAppResponse> {
    // Implementation for WhatsApp Business API
  }
}

// Referral tracking service
export class ReferralService {
  async trackReferral(
    tenantId: string,
    productId: string,
    externalUrl: string
  ): Promise<ReferralTrackingResult> {
    // Implementation for external website referral tracking
  }
}
```

### 3. Database Schema Design

#### Core Entities
```typescript
// Tenant entity
export interface Tenant {
  id: string;
  name: string;
  subdomain: string;
  config: TenantConfig;
  created_at: Date;
  updated_at: Date;
}

// Tour entity
export interface Tour {
  id: string;
  tenant_id: string;
  store_id: string;
  title: string;
  description: string;
  tour_type: 'panorama' | '3d' | 'video' | 'embed';
  tour_url: string;
  hotspots: Hotspot[];
  metadata: Record<string, any>;
  created_at: Date;
  updated_at: Date;
}

// Product tour integration
export interface ProductTour {
  id: string;
  product_id: string;
  tour_id: string;
  hotspot_position: { x: number; y: number; z?: number };
  interaction_type: 'info' | 'whatsapp_inquiry' | 'external_link' | 'details';
}

// Product inquiry tracking
export interface ProductInquiry {
  id: string;
  product_id: string;
  tenant_id: string;
  customer_info: CustomerInfo;
  inquiry_type: 'whatsapp' | 'external_referral';
  status: 'sent' | 'responded' | 'converted';
  created_at: Date;
  updated_at: Date;
}

// Referral analytics
export interface ReferralAnalytics {
  id: string;
  tenant_id: string;
  product_id: string;
  referral_url: string;
  clicks: number;
  conversions: number;
  revenue_tracked?: number;
  created_at: Date;
}
```

### 4. API Development

#### RESTful API Structure
```typescript
// Store API routes
/api/stores/[storeId]/tours
/api/stores/[storeId]/products
/api/stores/[storeId]/config
/api/stores/[storeId]/whatsapp-config
/api/stores/[storeId]/external-website

// Product inquiry routes
/api/products/[productId]/whatsapp-inquiry
/api/products/[productId]/external-referral
/api/inquiries/[inquiryId]/status

// Tenant API routes
/api/admin/tenants
/api/admin/tenants/[tenantId]/stores
/api/admin/tenants/[tenantId]/analytics
/api/admin/tenants/[tenantId]/referrals
```

#### GraphQL Schema (if needed)
```graphql
type Store {
  id: ID!
  name: String!
  description: String
  tours: [Tour!]!
  products: [Product!]!
  config: StoreConfig!
}

type Tour {
  id: ID!
  title: String!
  tourType: TourType!
  url: String!
  hotspots: [Hotspot!]!
}
```

### 5. Authentication & Authorization

#### JWT Implementation
```typescript
export interface JWTPayload {
  userId: string;
  tenantId: string;
  role: 'admin' | 'store_owner' | 'customer';
  permissions: string[];
}

export function verifyTenantAccess(req: AuthenticatedRequest): boolean {
  const { tenantId } = req.user;
  const requestedTenantId = req.params.tenantId || req.body.tenantId;
  
  return tenantId === requestedTenantId || req.user.role === 'super_admin';
}
```

#### Role-Based Access Control
```typescript
export const permissions = {
  'store_owner': ['read:store', 'write:store', 'read:products', 'write:products', 'read:inquiries', 'write:whatsapp-config'],
  'admin': ['read:tenant', 'write:tenant', 'read:analytics', 'read:referrals'],
  'customer': ['read:public', 'write:inquiries']
};
```

### 6. Tour Engine Integration

#### Abstract Tour Player
```typescript
export abstract class TourPlayer {
  abstract initialize(container: HTMLElement, config: TourConfig): Promise<void>;
  abstract loadTour(tourUrl: string): Promise<void>;
  abstract addHotspot(hotspot: Hotspot): void;
  abstract onHotspotClick(callback: (hotspot: Hotspot) => void): void;
  abstract destroy(): void;
}

export class PanoravenPlayer extends TourPlayer {
  // Implementation for Panoraven tours
}

export class ThreeDVistaPlayer extends TourPlayer {
  // Implementation for 3DVista tours
}
```

#### Tour Factory
```typescript
export class TourPlayerFactory {
  static create(type: TourType): TourPlayer {
    switch (type) {
      case 'panoraven':
        return new PanoravenPlayer();
      case '3dvista':
        return new ThreeDVistaPlayer();
      case 'custom':
        return new CustomWebGLPlayer();
      default:
        throw new Error(`Unsupported tour type: ${type}`);
    }
  }
}
```

### 7. Communication & Referral Integration

#### WhatsApp Business API Implementation
```typescript
export class WhatsAppService {
  private whatsappClient: WhatsAppBusinessAPI;

  async sendProductInquiry(
    storeConfig: WhatsAppConfig,
    productData: ProductData,
    customerMessage: string
  ): Promise<WhatsAppResult> {
    const message = this.formatProductInquiryMessage(productData, customerMessage);
    return this.whatsappClient.sendMessage(storeConfig.businessNumber, message);
  }

  private formatProductInquiryMessage(product: ProductData, customerMessage: string): string {
    return `New product inquiry:\n\nProduct: ${product.name}\nPrice: ${product.price}\nCustomer Message: ${customerMessage}\n\nView Product: ${product.tourUrl}`;
  }
}

#### External Website Referral Tracking
```typescript
export class ReferralTrackingService {
  async generateReferralUrl(
    productId: string,
    externalUrl: string,
    tenantId: string
  ): Promise<string> {
    const trackingParams = new URLSearchParams({
      utm_source: 'virtualrealtour',
      utm_medium: 'referral',
      utm_campaign: tenantId,
      product_id: productId
    });

    return `${externalUrl}?${trackingParams.toString()}`;
  }

  async trackClick(referralId: string): Promise<void> {
    await this.updateReferralAnalytics(referralId, 'click');
  }
}
```

### 8. Performance Optimization

#### Caching Strategy
```typescript
export class CacheService {
  // Redis implementation for tour data caching
  async cacheTourData(tourId: string, data: any, ttl: number = 3600): Promise<void> {
    await this.redis.setex(`tour:${tourId}`, ttl, JSON.stringify(data));
  }

  async getCachedTourData(tourId: string): Promise<any | null> {
    const cached = await this.redis.get(`tour:${tourId}`);
    return cached ? JSON.parse(cached) : null;
  }
}
```

#### Database Optimization
```typescript
// Implement database indexes for multi-tenant queries
export const indexes = [
  { fields: ['tenant_id', 'store_id'] },
  { fields: ['tenant_id', 'created_at'] },
  { fields: ['tour_type', 'tenant_id'] }
];
```

## File Organization

### Directory Structure
```
apps/api/src/
├── controllers/          # Route handlers
├── services/            # Business logic
├── models/              # Database models
├── middleware/          # Custom middleware
├── utils/               # Utility functions
├── types/               # TypeScript definitions
├── config/              # Configuration files
└── tests/               # Test files
```

### Naming Conventions
- **Files**: kebab-case (tour-service.ts)
- **Classes**: PascalCase (TourService)
- **Functions**: camelCase (getTourById)
- **Constants**: UPPER_SNAKE_CASE (DEFAULT_TOUR_CONFIG)
- **Interfaces**: PascalCase with 'I' prefix (ITourConfig)

## Testing Strategy

### Unit Tests
```typescript
describe('TourService', () => {
  it('should create a tour with proper tenant isolation', async () => {
    const tourService = new TourService();
    const tour = await tourService.createTour('tenant-1', mockTourData);
    
    expect(tour.tenant_id).toBe('tenant-1');
    expect(tour.id).toBeDefined();
  });
});
```

### Integration Tests
```typescript
describe('Tour API', () => {
  it('should not allow cross-tenant data access', async () => {
    const response = await request(app)
      .get('/api/stores/store-1/tours')
      .set('Authorization', `Bearer ${tenant2Token}`);
    
    expect(response.status).toBe(403);
  });
});
```

## Deployment Configuration

### Docker Configuration
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build
EXPOSE 3000
CMD ["npm", "start"]
```

### Environment Variables
```env
# Database
DATABASE_URL=postgresql://user:pass@localhost:5432/virtualrealtour
REDIS_URL=redis://localhost:6379

# Authentication
JWT_SECRET=your-jwt-secret
JWT_EXPIRY=24h

# Communication Services
WHATSAPP_BUSINESS_API_KEY=your-whatsapp-api-key
WHATSAPP_WEBHOOK_VERIFY_TOKEN=your-webhook-token

# Storage
AWS_S3_BUCKET=tour-assets
AWS_ACCESS_KEY_ID=...
AWS_SECRET_ACCESS_KEY=...
```

## Code Review Checklist

### Before Submitting Code
- [ ] All TypeScript errors resolved
- [ ] Unit tests written and passing
- [ ] Integration tests for API endpoints
- [ ] Performance impact assessed
- [ ] Security vulnerabilities checked
- [ ] Documentation updated
- [ ] Tenant isolation verified
- [ ] Error handling implemented
- [ ] Logging added for debugging
- [ ] Database migrations created (if needed)

### Code Quality Standards
- [ ] No hardcoded values (use environment variables)
- [ ] Proper error messages with context
- [ ] Input validation on all endpoints
- [ ] Rate limiting on public APIs
- [ ] SQL injection prevention
- [ ] XSS protection implemented
- [ ] CORS properly configured
- [ ] Audit trails for sensitive operations