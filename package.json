{"name": "virtualrealtour", "version": "1.0.0", "private": true, "description": "VirtualRealTour - Product Discovery and Referral Platform", "workspaces": ["apps/*", "packages/*"], "scripts": {"build": "turbo run build", "dev": "turbo run dev", "lint": "turbo run lint", "test": "turbo run test", "clean": "turbo run clean", "type-check": "turbo run type-check", "db:migrate": "cd apps/api && npm run db:migrate", "db:seed": "cd apps/api && npm run db:seed", "api:dev": "cd apps/api && npm run dev"}, "devDependencies": {"turbo": "^1.10.0", "@types/node": "^20.0.0", "typescript": "^5.0.0", "prettier": "^3.0.0", "eslint": "^8.0.0"}, "packageManager": "npm@9.0.0", "engines": {"node": ">=18.0.0"}}