import Link from 'next/link';
import Image from 'next/image';
import { 
  EyeIcon, 
  ShoppingBagIcon,
  ChatBubbleLeftRightIcon,
  ArrowTopRightOnSquareIcon
} from '@heroicons/react/24/outline';
import { Store } from '@/types';
import { motion } from 'framer-motion';

interface FeaturedStoresProps {
  stores: Store[];
  className?: string;
}

export function FeaturedStores({ stores, className = '' }: FeaturedStoresProps) {
  const getPlaceholderImage = (index: number) => {
    const images = [
      'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=400&h=300&fit=crop',
      'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop',
      'https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?w=400&h=300&fit=crop',
      'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=300&fit=crop',
    ];
    return images[index % images.length];
  };

  return (
    <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 ${className}`}>
      {stores.slice(0, 8).map((store, index) => (
        <motion.div
          key={store.id}
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: index * 0.1 }}
          viewport={{ once: true }}
          whileHover={{ y: -4 }}
          className="card-hover group"
        >
          {/* Store Image */}
          <div className="relative aspect-[4/3] overflow-hidden">
            <Image
              src={store.banner_url || getPlaceholderImage(index)}
              alt={store.name}
              fill
              className="object-cover group-hover:scale-105 transition-transform duration-300"
            />
            
            {/* Overlay */}
            <div className="absolute inset-0 bg-black/20 group-hover:bg-black/30 transition-colors duration-300" />
            
            {/* Status Badge */}
            <div className="absolute top-3 left-3">
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                store.status === 'active' 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-gray-100 text-gray-800'
              }`}>
                {store.status === 'active' ? 'Open' : 'Closed'}
              </span>
            </div>

            {/* Quick Actions */}
            <div className="absolute top-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              <div className="flex space-x-2">
                {store.communication_config?.whatsapp_enabled && (
                  <button className="w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center hover:bg-green-600 transition-colors">
                    <ChatBubbleLeftRightIcon className="w-4 h-4" />
                  </button>
                )}
                {store.communication_config?.external_website_enabled && (
                  <button className="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center hover:bg-blue-600 transition-colors">
                    <ArrowTopRightOnSquareIcon className="w-4 h-4" />
                  </button>
                )}
              </div>
            </div>

            {/* Store Logo */}
            {store.logo_url && (
              <div className="absolute bottom-3 left-3">
                <div className="w-12 h-12 bg-white rounded-lg overflow-hidden border-2 border-white shadow-lg">
                  <Image
                    src={store.logo_url}
                    alt={`${store.name} logo`}
                    width={48}
                    height={48}
                    className="object-cover"
                  />
                </div>
              </div>
            )}
          </div>

          {/* Store Content */}
          <div className="p-6">
            {/* Store Name */}
            <h3 className="text-lg font-semibold text-gray-900 mb-2 group-hover:text-black transition-colors">
              {store.name}
            </h3>

            {/* Description */}
            <p className="text-gray-600 text-sm mb-4 line-clamp-2">
              {store.description || 'Discover amazing products through immersive virtual experiences.'}
            </p>

            {/* Store Stats */}
            <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
              <div className="flex items-center space-x-1">
                <EyeIcon className="w-4 h-4" />
                <span>{store._count?.tours || 0} tours</span>
              </div>
              <div className="flex items-center space-x-1">
                <ShoppingBagIcon className="w-4 h-4" />
                <span>{store._count?.products || 0} products</span>
              </div>
            </div>

            {/* Features */}
            <div className="flex flex-wrap gap-1 mb-4">
              {store.communication_config?.whatsapp_enabled && (
                <span className="px-2 py-1 bg-green-100 text-green-700 text-xs rounded-full">
                  WhatsApp
                </span>
              )}
              {store.communication_config?.external_website_enabled && (
                <span className="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full">
                  Website
                </span>
              )}
              {store.communication_config?.track_referrals && (
                <span className="px-2 py-1 bg-purple-100 text-purple-700 text-xs rounded-full">
                  Referrals
                </span>
              )}
            </div>

            {/* Action Buttons */}
            <div className="flex space-x-2">
              <Link
                href={`/stores/${store.slug}`}
                className="btn-primary flex-1 text-center text-sm group-hover:bg-gray-800 transition-colors"
              >
                Explore Store
              </Link>
              
              {store._count?.tours && store._count.tours > 0 && (
                <Link
                  href={`/stores/${store.slug}/tours`}
                  className="btn-secondary flex-1 text-center text-sm"
                >
                  View Tours
                </Link>
              )}
            </div>
          </div>
        </motion.div>
      ))}
    </div>
  );
}
