{"name": "@virtualrealtour/database", "version": "1.0.0", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "type-check": "tsc --noEmit", "db:generate": "prisma generate", "db:migrate": "prisma migrate dev", "db:deploy": "prisma migrate deploy", "db:seed": "tsx seed.ts", "db:studio": "prisma studio"}, "dependencies": {"@prisma/client": "^5.0.0"}, "devDependencies": {"prisma": "^5.0.0", "typescript": "^5.0.0", "tsx": "^3.12.0"}}