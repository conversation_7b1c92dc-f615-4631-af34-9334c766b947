{"name": "@virtualrealtour/api", "version": "1.0.0", "main": "dist/index.js", "scripts": {"build": "tsc", "dev": "tsx watch src/index.ts", "start": "node dist/index.js", "clean": "rm -rf dist", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "db:migrate": "cd ../../packages/database && npm run db:migrate", "db:seed": "cd ../../packages/database && npm run db:seed", "db:generate": "cd ../../packages/database && npm run db:generate"}, "dependencies": {"express": "^4.18.0", "cors": "^2.8.5", "helmet": "^7.0.0", "morgan": "^1.10.0", "compression": "^1.7.4", "express-rate-limit": "^6.8.0", "express-validator": "^7.0.0", "jsonwebtoken": "^9.0.0", "bcryptjs": "^2.4.3", "dotenv": "^16.3.0", "winston": "^3.10.0", "axios": "^1.5.0", "uuid": "^9.0.0"}, "devDependencies": {"@types/express": "^4.17.0", "@types/cors": "^2.8.0", "@types/morgan": "^1.9.0", "@types/compression": "^1.7.0", "@types/jsonwebtoken": "^9.0.0", "@types/bcryptjs": "^2.4.0", "@types/uuid": "^9.0.0", "@types/jest": "^29.5.0", "@types/supertest": "^2.0.0", "typescript": "^5.0.0", "tsx": "^3.12.0", "jest": "^29.5.0", "supertest": "^6.3.0", "ts-jest": "^29.1.0"}}