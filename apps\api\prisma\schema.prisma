// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Tenant {
  id        String   @id @default(cuid())
  name      String
  subdomain String   @unique
  domain    String?
  config    Json
  status    String   @default("active") // active, inactive, suspended
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relations
  stores           Store[]
  tours            Tour[]
  products         Product[]
  users            User[]
  productInquiries ProductInquiry[]
  referralAnalytics ReferralAnalytics[]

  @@map("tenants")
}

model Store {
  id                   String @id @default(cuid())
  tenantId             String @map("tenant_id")
  name                 String
  description          String?
  slug                 String
  logoUrl              String? @map("logo_url")
  bannerUrl            String? @map("banner_url")
  status               String  @default("active") // active, inactive
  communicationConfig  <PERSON><PERSON>    @map("communication_config")
  createdAt            DateTime @default(now()) @map("created_at")
  updatedAt            DateTime @updatedAt @map("updated_at")

  // Relations
  tenant            Tenant              @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  tours             Tour[]
  products          Product[]
  productInquiries  ProductInquiry[]
  referralAnalytics ReferralAnalytics[]

  @@unique([tenantId, slug])
  @@map("stores")
}

model Product {
  id                       String   @id @default(cuid())
  storeId                  String   @map("store_id")
  tenantId                 String   @map("tenant_id")
  name                     String
  description              String?
  price                    Decimal
  currency                 String   @default("USD")
  images                   String[]
  has3dModel               Boolean  @default(false) @map("has_3d_model")
  vrReady                  Boolean  @default(false) @map("vr_ready")
  video360Url              String?  @map("video_360_url")
  externalUrl              String?  @map("external_url")
  whatsappInquiryEnabled   Boolean  @default(true) @map("whatsapp_inquiry_enabled")
  referralTrackingEnabled  Boolean  @default(true) @map("referral_tracking_enabled")
  metadata                 Json     @default("{}")
  status                   String   @default("active") // active, inactive, draft
  createdAt                DateTime @default(now()) @map("created_at")
  updatedAt                DateTime @updatedAt @map("updated_at")

  // Relations
  store             Store               @relation(fields: [storeId], references: [id], onDelete: Cascade)
  tenant            Tenant              @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  productTours      ProductTour[]
  productInquiries  ProductInquiry[]
  referralAnalytics ReferralAnalytics[]

  @@map("products")
}

model Tour {
  id          String   @id @default(cuid())
  tenantId    String   @map("tenant_id")
  storeId     String   @map("store_id")
  title       String
  description String?
  tourType    String   @map("tour_type") // panorama, 3d, video, embed
  tourUrl     String   @map("tour_url")
  hotspots    Json     @default("[]")
  metadata    Json     @default("{}")
  status      String   @default("active") // active, inactive, draft
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // Relations
  tenant       Tenant        @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  store        Store         @relation(fields: [storeId], references: [id], onDelete: Cascade)
  productTours ProductTour[]

  @@map("tours")
}

model ProductTour {
  id              String @id @default(cuid())
  productId       String @map("product_id")
  tourId          String @map("tour_id")
  hotspotPosition Json   @map("hotspot_position")
  interactionType String @map("interaction_type") // info, whatsapp_inquiry, external_link, details
  actionData      Json?  @map("action_data")
  createdAt       DateTime @default(now()) @map("created_at")

  // Relations
  product Product @relation(fields: [productId], references: [id], onDelete: Cascade)
  tour    Tour    @relation(fields: [tourId], references: [id], onDelete: Cascade)

  @@unique([productId, tourId])
  @@map("product_tours")
}

model User {
  id        String   @id @default(cuid())
  tenantId  String   @map("tenant_id")
  email     String
  name      String
  role      String   // super_admin, tenant_admin, store_owner, customer
  status    String   @default("active") // active, inactive
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relations
  tenant Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@unique([tenantId, email])
  @@map("users")
}

model ProductInquiry {
  id                 String   @id @default(cuid())
  productId          String   @map("product_id")
  storeId            String   @map("store_id")
  tenantId           String   @map("tenant_id")
  customerName       String   @map("customer_name")
  customerEmail      String   @map("customer_email")
  customerPhone      String?  @map("customer_phone")
  inquiryType        String   @map("inquiry_type") // whatsapp, external_referral
  message            String
  status             String   @default("sent") // sent, responded, converted, closed
  whatsappMessageId  String?  @map("whatsapp_message_id")
  referralUrl        String?  @map("referral_url")
  createdAt          DateTime @default(now()) @map("created_at")
  updatedAt          DateTime @updatedAt @map("updated_at")

  // Relations
  product Product @relation(fields: [productId], references: [id], onDelete: Cascade)
  store   Store   @relation(fields: [storeId], references: [id], onDelete: Cascade)
  tenant  Tenant  @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@map("product_inquiries")
}

model ReferralAnalytics {
  id             String   @id @default(cuid())
  productId      String   @map("product_id")
  storeId        String   @map("store_id")
  tenantId       String   @map("tenant_id")
  referralUrl    String   @map("referral_url")
  clicks         Int      @default(0)
  conversions    Int      @default(0)
  revenueTracked Decimal? @map("revenue_tracked")
  utmSource      String   @map("utm_source")
  utmMedium      String   @map("utm_medium")
  utmCampaign    String   @map("utm_campaign")
  createdAt      DateTime @default(now()) @map("created_at")
  updatedAt      DateTime @updatedAt @map("updated_at")

  // Relations
  product Product @relation(fields: [productId], references: [id], onDelete: Cascade)
  store   Store   @relation(fields: [storeId], references: [id], onDelete: Cascade)
  tenant  Tenant  @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@unique([productId, utmSource, utmMedium, utmCampaign])
  @@map("referral_analytics")
}
