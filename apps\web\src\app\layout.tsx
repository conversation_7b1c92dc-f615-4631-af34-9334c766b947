import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import './globals.css';
import { Providers } from '@/components/providers';
import { Toaster } from 'react-hot-toast';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'VirtualRealTour - Immersive Virtual Shopping Experience',
  description: 'Discover products through immersive virtual tours and 360° experiences. Connect directly with stores via WhatsApp and explore like never before.',
  keywords: 'virtual tour, 360 video, virtual shopping, immersive experience, product discovery',
  authors: [{ name: 'VirtualRealTour' }],
  viewport: 'width=device-width, initial-scale=1',
  robots: 'index, follow',
  openGraph: {
    title: 'VirtualRealTour - Immersive Virtual Shopping Experience',
    description: 'Discover products through immersive virtual tours and 360° experiences.',
    type: 'website',
    locale: 'en_US',
    siteName: 'VirtualRealTour',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'VirtualRealTour - Immersive Virtual Shopping Experience',
    description: 'Discover products through immersive virtual tours and 360° experiences.',
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" className="h-full">
      <body className={`${inter.className} h-full bg-white text-gray-900 antialiased`}>
        <Providers>
          {children}
          <Toaster
            position="top-right"
            toastOptions={{
              duration: 4000,
              style: {
                background: '#1f2937',
                color: '#f9fafb',
                border: '1px solid #374151',
              },
              success: {
                iconTheme: {
                  primary: '#10b981',
                  secondary: '#f9fafb',
                },
              },
              error: {
                iconTheme: {
                  primary: '#ef4444',
                  secondary: '#f9fafb',
                },
              },
            }}
          />
        </Providers>
      </body>
    </html>
  );
}
