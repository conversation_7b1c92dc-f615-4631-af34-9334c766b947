import { Request, Response } from 'express';
import { prisma } from '../../../packages/database/dist/index';
import ProductService from '@/services/ProductService';
import ReferralService from '@/services/ReferralService';
import ResponseHelper from '@/utils/response';
import { asyncHandler } from '@/middleware/errorHandler';
import { StoreNotFoundError } from '@/utils/errors';
import type { Store, StoreCommunicationConfig } from '../../../packages/types/dist/index';
import logger from '@/utils/logger';

export class StoreController {
  private productService: ProductService;
  private referralService: ReferralService;

  constructor() {
    this.productService = new ProductService();
    this.referralService = new ReferralService();
  }

  /**
   * Get all stores for a tenant
   */
  getStores = asyncHandler(async (req: Request, res: Response) => {
    const tenantId = req.tenant!.id;

    const options = {
      page: parseInt(req.query.page as string) || 1,
      limit: parseInt(req.query.limit as string) || 20,
      sortBy: req.query.sortBy as string || 'created_at',
      sortOrder: req.query.sortOrder as 'asc' | 'desc' || 'desc',
    };

    const skip = (options.page - 1) * options.limit;

    const whereClause: any = {
      tenantId: tenantId,
      status: 'active',
    };

    // Add search filter if provided
    if (req.query.search) {
      whereClause.OR = [
        { name: { contains: req.query.search as string, mode: 'insensitive' } },
        { description: { contains: req.query.search as string, mode: 'insensitive' } },
      ];
    }

    const [stores, total] = await Promise.all([
      prisma.store.findMany({
        where: whereClause,
        skip,
        take: options.limit,
        orderBy: {
          [options.sortBy]: options.sortOrder,
        },
        include: {
          _count: {
            select: {
              products: true,
              tours: true,
            },
          },
        },
      }),
      prisma.store.count({
        where: whereClause,
      }),
    ]);

    logger.info(`Retrieved ${stores.length} stores for tenant ${tenantId}`);

    return ResponseHelper.paginated(
      res,
      stores as Store[],
      options.page,
      options.limit,
      total,
      'Stores retrieved successfully'
    );
  });

  /**
   * Get a single store by ID or slug
   */
  getStore = asyncHandler(async (req: Request, res: Response) => {
    const { storeId } = req.params;
    const tenantId = req.tenant!.id;

    const store = await prisma.store.findFirst({
      where: {
        OR: [
          { id: storeId },
          { slug: storeId },
        ],
        tenantId: tenantId,
        status: 'active',
      },
      include: {
        _count: {
          select: {
            products: true,
            tours: true,
            productInquiries: true,
          },
        },
      },
    });

    if (!store) {
      throw new StoreNotFoundError(storeId);
    }

    logger.info(`Retrieved store ${store.id} (${store.name})`);
    return ResponseHelper.success(res, store as Store, 'Store retrieved successfully');
  });

  /**
   * Get store communication configuration
   */
  getStoreConfig = asyncHandler(async (req: Request, res: Response) => {
    const { storeId } = req.params;
    const tenantId = req.tenant!.id;

    const store = await prisma.store.findFirst({
      where: {
        id: storeId,
        tenantId: tenantId,
      },
      select: {
        id: true,
        name: true,
        communicationConfig: true,
      },
    });

    if (!store) {
      throw new StoreNotFoundError(storeId);
    }

    return ResponseHelper.success(
      res,
      store.communicationConfig as StoreCommunicationConfig,
      'Store configuration retrieved successfully'
    );
  });

  /**
   * Update store communication configuration
   */
  updateStoreConfig = asyncHandler(async (req: Request, res: Response) => {
    const { storeId } = req.params;
    const tenantId = req.tenant!.id;
    const configUpdate = req.body;

    // Verify store exists and belongs to tenant
    const existingStore = await prisma.store.findFirst({
      where: {
        id: storeId,
        tenantId: tenantId,
      },
    });

    if (!existingStore) {
      throw new StoreNotFoundError(storeId);
    }

    // Merge with existing configuration
    const currentConfig = existingStore.communicationConfig as StoreCommunicationConfig;
    const updatedConfig = {
      ...currentConfig,
      ...configUpdate,
    };

    const updatedStore = await prisma.store.update({
      where: { id: storeId },
      data: {
        communicationConfig: updatedConfig,
        updatedAt: new Date(),
      },
      select: {
        id: true,
        name: true,
        communicationConfig: true,
      },
    });

    logger.info(`Updated configuration for store ${storeId}`, {
      storeId,
      tenantId,
      updatedFields: Object.keys(configUpdate),
    });

    return ResponseHelper.success(
      res,
      updatedStore.communicationConfig as StoreCommunicationConfig,
      'Store configuration updated successfully'
    );
  });

  /**
   * Get store inquiries
   */
  getStoreInquiries = asyncHandler(async (req: Request, res: Response) => {
    const { storeId } = req.params;
    const tenantId = req.tenant!.id;

    const options = {
      page: parseInt(req.query.page as string) || 1,
      limit: parseInt(req.query.limit as string) || 20,
      sortBy: req.query.sortBy as string || 'created_at',
      sortOrder: req.query.sortOrder as 'asc' | 'desc' || 'desc',
      filters: {},
    };

    // Add status filter if provided
    if (req.query.status) {
      options.filters = { status: req.query.status as string };
    }

    const result = await this.productService.getStoreInquiries(storeId, tenantId, options);

    return ResponseHelper.paginated(
      res,
      result.inquiries,
      result.pagination.page,
      result.pagination.limit,
      result.pagination.total,
      'Store inquiries retrieved successfully'
    );
  });

  /**
   * Get store analytics
   */
  getStoreAnalytics = asyncHandler(async (req: Request, res: Response) => {
    const { storeId } = req.params;
    const tenantId = req.tenant!.id;

    // Verify store exists
    const store = await prisma.store.findFirst({
      where: {
        id: storeId,
        tenantId: tenantId,
      },
    });

    if (!store) {
      throw new StoreNotFoundError(storeId);
    }

    const [productStats, referralStats] = await Promise.all([
      this.productService.getProductStats(storeId, tenantId),
      this.referralService.getReferralStats(tenantId, storeId),
    ]);

    const analytics = {
      products: productStats,
      referrals: referralStats,
      overview: {
        totalProducts: productStats.totalProducts,
        totalInquiries: productStats.totalInquiries,
        totalReferrals: referralStats.totalReferrals,
        conversionRate: referralStats.conversionRate,
      },
    };

    return ResponseHelper.success(res, analytics, 'Store analytics retrieved successfully');
  });

  /**
   * Get store referral analytics
   */
  getStoreReferralAnalytics = asyncHandler(async (req: Request, res: Response) => {
    const { storeId } = req.params;
    const tenantId = req.tenant!.id;

    const analytics = await this.referralService.getStoreReferralAnalytics(storeId, tenantId);

    return ResponseHelper.success(res, analytics, 'Store referral analytics retrieved successfully');
  });
}

export default StoreController;
