'use client';

import { motion } from 'framer-motion';
import { PlayIcon, ArrowRightIcon } from '@heroicons/react/24/outline';
import Link from 'next/link';

export function HeroSection() {
  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden bg-black">
      {/* Background Video - Replicating ovrworldwide.com */}
      <div className="absolute inset-0 z-0">
        <div className="absolute inset-0 bg-black/50 z-10" />
        <video
          autoPlay
          muted
          loop
          playsInline
          className="w-full h-full object-cover"
          poster="https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=1920&q=80"
        >
          <source src="https://player.vimeo.com/external/434045526.sd.mp4?s=c27eecc69a27dbc4ff2b87d38afc35f1a9a1c1e5&profile_id=165&oauth2_token_id=57447761" type="video/mp4" />
        </video>
      </div>

      {/* Hero Content - Exact replica of ovrworldwide.com layout */}
      <div className="relative z-20 text-center text-white px-4 sm:px-6 lg:px-8 max-w-6xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          {/* Main Headline */}
          <h1 className="text-4xl sm:text-5xl lg:text-7xl font-bold mb-6 leading-tight">
            IMMERSIVE
            <br />
            <span className="text-blue-400">VIRTUAL EXPERIENCES</span>
            <br />
            FOR NIGERIA
          </h1>
          
          {/* Subheadline */}
          <p className="text-xl sm:text-2xl mb-8 text-gray-200 max-w-4xl mx-auto leading-relaxed font-light">
            We create cutting-edge virtual tours, 360° experiences, and immersive digital solutions 
            for businesses across Lagos, Abuja, and Port Harcourt.
          </p>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
            <Link 
              href="/services" 
              className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-lg text-lg font-semibold transition-all duration-300 transform hover:scale-105 group"
            >
              Explore Our Services
              <ArrowRightIcon className="w-5 h-5 ml-2 inline group-hover:translate-x-1 transition-transform" />
            </Link>
            
            <button 
              type="button"
              className="bg-transparent border-2 border-white text-white hover:bg-white hover:text-black px-8 py-4 rounded-lg text-lg font-semibold transition-all duration-300 group"
            >
              <PlayIcon className="w-5 h-5 mr-2 inline" />
              Watch Demo
            </button>
          </div>

          {/* Key Features - Replicating their feature highlights */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20"
            >
              <div className="text-3xl font-bold text-blue-400 mb-2">360°</div>
              <div className="text-lg font-semibold mb-2">Virtual Tours</div>
              <div className="text-sm text-gray-300">Immersive experiences that showcase every detail</div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20"
            >
              <div className="text-3xl font-bold text-blue-400 mb-2">8K</div>
              <div className="text-lg font-semibold mb-2">Ultra HD Quality</div>
              <div className="text-sm text-gray-300">Crystal clear visuals for maximum impact</div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
              className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20"
            >
              <div className="text-3xl font-bold text-blue-400 mb-2">24/7</div>
              <div className="text-lg font-semibold mb-2">Always Accessible</div>
              <div className="text-sm text-gray-300">Your virtual space, available anytime</div>
            </motion.div>
          </div>
        </motion.div>

        {/* Scroll Indicator */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 1.5, duration: 0.5 }}
          className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
        >
          <div className="w-6 h-10 border-2 border-white/50 rounded-full flex justify-center">
            <div className="w-1 h-3 bg-white/70 rounded-full mt-2 animate-bounce" />
          </div>
        </motion.div>
      </div>

      {/* Floating Elements - Adding visual interest like ovrworldwide.com */}
      <div className="absolute inset-0 z-10 pointer-events-none">
        <motion.div
          animate={{ 
            y: [0, -20, 0],
            rotate: [0, 5, 0]
          }}
          transition={{ 
            duration: 6,
            repeat: Infinity,
            ease: "easeInOut"
          }}
          className="absolute top-1/4 left-1/4 w-20 h-20 bg-blue-500/20 rounded-full blur-xl"
        />
        <motion.div
          animate={{ 
            y: [0, 20, 0],
            rotate: [0, -5, 0]
          }}
          transition={{ 
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut"
          }}
          className="absolute top-1/3 right-1/4 w-32 h-32 bg-purple-500/20 rounded-full blur-xl"
        />
        <motion.div
          animate={{ 
            y: [0, -15, 0],
            x: [0, 10, 0]
          }}
          transition={{ 
            duration: 7,
            repeat: Infinity,
            ease: "easeInOut"
          }}
          className="absolute bottom-1/3 left-1/3 w-24 h-24 bg-cyan-500/20 rounded-full blur-xl"
        />
      </div>
    </section>
  );
}
