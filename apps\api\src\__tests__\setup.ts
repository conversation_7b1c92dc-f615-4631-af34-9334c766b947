import { prisma } from '@virtualrealtour/database';

// Setup test environment
beforeAll(async () => {
  // Connect to test database
  await prisma.$connect();
});

// Cleanup after all tests
afterAll(async () => {
  // Disconnect from database
  await prisma.$disconnect();
});

// Clean up after each test
afterEach(async () => {
  // Clean up test data if needed
  // Note: In a real test environment, you might want to use a separate test database
  // and clean up data between tests
});
