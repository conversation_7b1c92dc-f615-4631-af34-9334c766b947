import { prisma } from '../lib/prisma.js';
import type {
  Product,
  ProductInquiry,
  QueryOptions,
  CreateProductInquiryRequest
} from '../types/index.js';
import { ProductNotFoundError, StoreNotFoundError } from '../utils/errors.js';
import logger from '../utils/logger.js';

export class ProductService {
  /**
   * Get products for a store with pagination and filtering
   */
  async getStoreProducts(
    storeId: string,
    tenantId: string,
    options: QueryOptions = {}
  ) {
    try {
      const {
        page = 1,
        limit = 20,
        sortBy = 'created_at',
        sortOrder = 'desc',
        filters = {},
      } = options;

      const skip = (page - 1) * limit;

      const whereClause: any = {
        store_id: storeId,
        tenant_id: tenantId,
        status: 'active',
        ...filters,
      };

      const [products, total] = await Promise.all([
        prisma.product.findMany({
          where: whereClause,
          skip,
          take: limit,
          orderBy: {
            [sortBy]: sortOrder,
          },
          include: {
            store: {
              select: {
                name: true,
                slug: true,
                communicationConfig: true,
              },
            },
          },
        }),
        prisma.product.count({
          where: whereClause,
        }),
      ]);

      logger.info(`Retrieved ${products.length} products for store ${storeId}`);

      return {
        products: products as any,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      logger.error('Failed to get store products:', error);
      throw error;
    }
  }

  /**
   * Get a single product by ID
   */
  async getProduct(productId: string, tenantId: string): Promise<Product> {
    try {
      const product = await prisma.product.findFirst({
        where: {
          id: productId,
          tenantId: tenantId,
          status: 'active',
        },
        include: {
          store: {
            select: {
              id: true,
              name: true,
              slug: true,
              communicationConfig: true,
            },
          },
        },
      });

      if (!product) {
        throw new ProductNotFoundError(productId);
      }

      logger.info(`Retrieved product ${productId}`);
      return product as any;
    } catch (error) {
      logger.error('Failed to get product:', error);
      throw error;
    }
  }

  /**
   * Search products across all stores in a tenant
   */
  async searchProducts(
    tenantId: string,
    searchTerm: string,
    options: QueryOptions = {}
  ) {
    try {
      const {
        page = 1,
        limit = 20,
        sortBy = 'created_at',
        sortOrder = 'desc',
        filters = {},
      } = options;

      const skip = (page - 1) * limit;

      const whereClause: any = {
        tenantId: tenantId,
        status: 'active',
        OR: [
          {
            name: {
              contains: searchTerm,
              mode: 'insensitive',
            },
          },
          {
            description: {
              contains: searchTerm,
              mode: 'insensitive',
            },
          },
        ],
        ...filters,
      };

      const [products, total] = await Promise.all([
        prisma.product.findMany({
          where: whereClause,
          skip,
          take: limit,
          orderBy: {
            [sortBy]: sortOrder,
          },
          include: {
            store: {
              select: {
                name: true,
                slug: true,
                communicationConfig: true,
              },
            },
          },
        }),
        prisma.product.count({
          where: whereClause,
        }),
      ]);

      logger.info(`Found ${products.length} products for search term: ${searchTerm}`);

      return {
        products: products as any,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      logger.error('Failed to search products:', error);
      throw error;
    }
  }

  /**
   * Create a product inquiry
   */
  async createProductInquiry(
    productId: string,
    tenantId: string,
    inquiryData: CreateProductInquiryRequest
  ): Promise<ProductInquiry> {
    try {
      // Verify product exists and is active
      const product = await this.getProduct(productId, tenantId);

      const inquiry = await prisma.productInquiry.create({
        data: {
          productId: productId,
          storeId: product.store_id,
          tenantId: tenantId,
          customerName: inquiryData.customer_name,
          customerEmail: inquiryData.customer_email,
          customerPhone: inquiryData.customer_phone,
          inquiryType: inquiryData.inquiry_type,
          message: inquiryData.message,
          status: 'sent',
        },
      });

      logger.info(`Created product inquiry ${inquiry.id} for product ${productId}`, {
        inquiryId: inquiry.id,
        productId,
        storeId: product.store_id,
        tenantId,
        inquiryType: inquiryData.inquiry_type,
      });

      return inquiry as any;
    } catch (error) {
      logger.error('Failed to create product inquiry:', error);
      throw error;
    }
  }

  /**
   * Get product inquiries for a store
   */
  async getStoreInquiries(
    storeId: string,
    tenantId: string,
    options: QueryOptions = {}
  ) {
    try {
      const {
        page = 1,
        limit = 20,
        sortBy = 'created_at',
        sortOrder = 'desc',
        filters = {},
      } = options;

      const skip = (page - 1) * limit;

      const whereClause: any = {
        storeId: storeId,
        tenantId: tenantId,
        ...filters,
      };

      const [inquiries, total] = await Promise.all([
        prisma.productInquiry.findMany({
          where: whereClause,
          skip,
          take: limit,
          orderBy: {
            [sortBy]: sortOrder,
          },
          include: {
            product: {
              select: {
                name: true,
                price: true,
                currency: true,
                images: true,
              },
            },
          },
        }),
        prisma.productInquiry.count({
          where: whereClause,
        }),
      ]);

      logger.info(`Retrieved ${inquiries.length} inquiries for store ${storeId}`);

      return {
        inquiries: inquiries as any,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      logger.error('Failed to get store inquiries:', error);
      throw error;
    }
  }

  /**
   * Update inquiry status
   */
  async updateInquiryStatus(
    inquiryId: string,
    tenantId: string,
    status: string
  ): Promise<ProductInquiry> {
    try {
      const inquiry = await prisma.productInquiry.updateMany({
        where: {
          id: inquiryId,
          tenantId: tenantId,
        },
        data: {
          status: status,
          updatedAt: new Date(),
        },
      });

      if (inquiry.count === 0) {
        throw new ProductNotFoundError(`Inquiry ${inquiryId}`);
      }

      const updatedInquiry = await prisma.productInquiry.findUnique({
        where: { id: inquiryId },
        include: {
          product: {
            select: {
              name: true,
              price: true,
              currency: true,
            },
          },
        },
      });

      logger.info(`Updated inquiry ${inquiryId} status to ${status}`);
      return updatedInquiry as any;
    } catch (error) {
      logger.error('Failed to update inquiry status:', error);
      throw error;
    }
  }

  /**
   * Get product statistics for a store
   */
  async getProductStats(storeId: string, tenantId: string) {
    try {
      const [
        totalProducts,
        activeProducts,
        totalInquiries,
        recentInquiries,
      ] = await Promise.all([
        prisma.product.count({
          where: { storeId: storeId, tenantId: tenantId },
        }),
        prisma.product.count({
          where: { storeId: storeId, tenantId: tenantId, status: 'active' },
        }),
        prisma.productInquiry.count({
          where: { storeId: storeId, tenantId: tenantId },
        }),
        prisma.productInquiry.count({
          where: {
            storeId: storeId,
            tenantId: tenantId,
            createdAt: {
              gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // Last 7 days
            },
          },
        }),
      ]);

      return {
        totalProducts,
        activeProducts,
        totalInquiries,
        recentInquiries,
        inquiryRate: totalProducts > 0 ? (totalInquiries / totalProducts) : 0,
      };
    } catch (error) {
      logger.error('Failed to get product stats:', error);
      throw error;
    }
  }
}

export default ProductService;
