{"name": "@virtualrealtour/web", "version": "1.0.0", "private": true, "scripts": {"build": "next build", "dev": "next dev", "lint": "next lint", "start": "next start", "type-check": "tsc --noEmit"}, "dependencies": {"@headlessui/react": "^1.7.0", "@heroicons/react": "^2.0.0", "@hookform/resolvers": "^3.3.0", "@supabase/supabase-js": "^2.38.0", "@tailwindcss/forms": "^0.5.0", "@tailwindcss/typography": "^0.5.0", "@types/leaflet": "^1.9.0", "@types/node": "^20.0.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "autoprefixer": "^10.4.0", "axios": "^1.5.0", "clsx": "^2.0.0", "date-fns": "^2.30.0", "embla-carousel-react": "^8.0.0", "framer-motion": "^10.0.0", "leaflet": "^1.9.0", "lucide-react": "^0.263.0", "next": "^14.0.0", "postcss": "^8.4.0", "react": "^18.0.0", "react-dom": "^18.0.0", "react-hook-form": "^7.45.0", "react-hot-toast": "^2.4.0", "react-intersection-observer": "^9.16.0", "react-leaflet": "^4.2.0", "react-query": "^3.39.0", "recharts": "^2.8.0", "tailwindcss": "^3.3.0", "typescript": "^5.0.0", "zod": "^3.22.0"}, "devDependencies": {"@types/leaflet": "^1.9.0", "eslint": "^8.0.0", "eslint-config-next": "^14.0.0"}}