'use client';

import { useEffect, useState } from 'react';
import { Store } from '@/types';
import { MapPinIcon, EyeIcon } from '@heroicons/react/24/outline';

interface StoreMapProps {
  stores: Store[];
  className?: string;
}

// Mock coordinates for demo stores
const getMockCoordinates = (index: number) => {
  const baseCoords = [
    { lat: 37.7749, lng: -122.4194 }, // San Francisco
    { lat: 37.7849, lng: -122.4094 }, // SF - North
    { lat: 37.7649, lng: -122.4294 }, // SF - South
    { lat: 37.7749, lng: -122.4094 }, // SF - East
  ];
  return baseCoords[index % baseCoords.length];
};

export function StoreMap({ stores, className = '' }: StoreMapProps) {
  const [selectedStore, setSelectedStore] = useState<Store | null>(null);
  const [mapLoaded, setMapLoaded] = useState(false);

  // For now, we'll create a simple interactive map placeholder
  // In a real implementation, you would integrate with Google Maps, Mapbox, or Leaflet

  useEffect(() => {
    // Simulate map loading
    const timer = setTimeout(() => setMapLoaded(true), 1000);
    return () => clearTimeout(timer);
  }, []);

  if (!mapLoaded) {
    return (
      <div className={`w-full h-full bg-gray-100 rounded-lg flex items-center justify-center ${className}`}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-2"></div>
          <p className="text-gray-600">Loading interactive map...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`relative w-full h-full bg-gradient-to-br from-blue-50 to-green-50 rounded-lg overflow-hidden ${className}`}>
      {/* Map Background */}
      <div className="absolute inset-0 opacity-20">
        <svg
          viewBox="0 0 400 300"
          className="w-full h-full"
          style={{ filter: 'blur(1px)' }}
        >
          {/* Street grid pattern */}
          <defs>
            <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
              <path d="M 40 0 L 0 0 0 40" fill="none" stroke="#94a3b8" strokeWidth="1" />
            </pattern>
          </defs>
          <rect width="100%" height="100%" fill="url(#grid)" />

          {/* Mock streets */}
          <path d="M0,100 Q200,80 400,120" stroke="#64748b" strokeWidth="3" fill="none" />
          <path d="M0,200 Q200,180 400,220" stroke="#64748b" strokeWidth="3" fill="none" />
          <path d="M100,0 Q120,150 140,300" stroke="#64748b" strokeWidth="3" fill="none" />
          <path d="M300,0 Q320,150 340,300" stroke="#64748b" strokeWidth="3" fill="none" />
        </svg>
      </div>

      {/* Store Markers */}
      {stores.slice(0, 8).map((store, index) => {
        const coords = getMockCoordinates(index);
        const x = ((coords.lng + 122.5) / 0.1) * 100; // Convert to percentage
        const y = ((37.8 - coords.lat) / 0.1) * 100; // Convert to percentage

        return (
          <div
            key={store.id}
            className="absolute transform -translate-x-1/2 -translate-y-1/2 cursor-pointer group"
            style={{
              left: `${Math.max(10, Math.min(90, x))}%`,
              top: `${Math.max(10, Math.min(90, y))}%`
            }}
            onClick={() => setSelectedStore(selectedStore?.id === store.id ? null : store)}
          >
            {/* Marker */}
            <div className={`relative ${selectedStore?.id === store.id ? 'z-20' : 'z-10'}`}>
              <div className={`w-8 h-8 rounded-full border-2 border-white shadow-lg flex items-center justify-center transition-all duration-200 ${selectedStore?.id === store.id
                ? 'bg-red-500 scale-125'
                : 'bg-blue-500 hover:bg-blue-600 hover:scale-110'
                }`}>
                <MapPinIcon className="w-4 h-4 text-white" />
              </div>

              {/* Pulse animation */}
              <div className="absolute inset-0 rounded-full bg-blue-400 animate-ping opacity-30"></div>
            </div>

            {/* Store Info Popup */}
            {selectedStore?.id === store.id && (
              <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 w-64 bg-white rounded-lg shadow-xl border border-gray-200 p-4 z-30">
                <div className="flex items-start space-x-3">
                  {/* Store Logo */}
                  {store.logo_url ? (
                    <div className="w-12 h-12 rounded-lg bg-gray-200 flex items-center justify-center flex-shrink-0">
                      <span className="text-xs font-semibold text-gray-600">
                        {store.name.charAt(0)}
                      </span>
                    </div>
                  ) : (
                    <div className="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center flex-shrink-0">
                      <MapPinIcon className="w-6 h-6 text-gray-400" />
                    </div>
                  )}

                  <div className="flex-1 min-w-0">
                    <h3 className="font-semibold text-gray-900 truncate">{store.name}</h3>
                    <p className="text-sm text-gray-600 line-clamp-2 mb-2">
                      {store.description || 'Explore virtual tours and products'}
                    </p>

                    <div className="flex items-center justify-between text-xs text-gray-500 mb-3">
                      <div className="flex items-center space-x-1">
                        <EyeIcon className="w-3 h-3" />
                        <span>{store._count?.tours || 0} tours</span>
                      </div>
                      <span className={`px-2 py-1 rounded-full ${store.status === 'active'
                        ? 'bg-green-100 text-green-700'
                        : 'bg-gray-100 text-gray-700'
                        }`}>
                        {store.status === 'active' ? 'Open' : 'Closed'}
                      </span>
                    </div>

                    <button
                      type="button"
                      onClick={(e) => {
                        e.stopPropagation();
                        window.open(`/stores/${store.slug}`, '_blank');
                      }}
                      className="w-full btn-primary text-xs py-2"
                    >
                      Visit Store
                    </button>
                  </div>
                </div>

                {/* Arrow */}
                <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-white"></div>
              </div>
            )}
          </div>
        );
      })}

      {/* Map Controls */}
      <div className="absolute top-4 right-4 bg-white rounded-lg shadow-lg border border-gray-200 p-2">
        <div className="flex flex-col space-y-1">
          <button type="button" className="w-8 h-8 flex items-center justify-center text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded">
            <span className="text-lg font-bold">+</span>
          </button>
          <button type="button" className="w-8 h-8 flex items-center justify-center text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded">
            <span className="text-lg font-bold">−</span>
          </button>
        </div>
      </div>

      {/* Map Legend */}
      <div className="absolute bottom-4 left-4 bg-white rounded-lg shadow-lg border border-gray-200 p-3">
        <div className="flex items-center space-x-2 text-sm">
          <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
          <span className="text-gray-700">Virtual Tour Available</span>
        </div>
      </div>

      {/* Store Count */}
      <div className="absolute top-4 left-4 bg-white rounded-lg shadow-lg border border-gray-200 px-3 py-2">
        <div className="text-sm font-medium text-gray-900">
          {stores.length} stores with virtual tours
        </div>
      </div>
    </div>
  );
}
