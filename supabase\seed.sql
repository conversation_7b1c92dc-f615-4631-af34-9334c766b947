-- Seed data for VirtualRealTour platform

-- Insert demo tenants
INSERT INTO tenants (id, name, subdomain, domain, config, status) VALUES
('550e8400-e29b-41d4-a716-446655440001', 'Demo Marketplace', 'demo', 'demo.virtualrealtour.com', 
 '{"theme": "default", "features": ["whatsapp", "referrals", "analytics"], "branding": {"primary_color": "#000000", "secondary_color": "#ffffff"}}', 'active'),
('550e8400-e29b-41d4-a716-446655440002', 'Luxury Stores', 'luxury', 'luxury.virtualrealtour.com', 
 '{"theme": "luxury", "features": ["whatsapp", "referrals", "analytics", "premium"], "branding": {"primary_color": "#1a1a1a", "secondary_color": "#f5f5f5"}}', 'active'),
('550e8400-e29b-41d4-a716-446655440003', 'Tech Showcase', 'tech', 'tech.virtualrealtour.com', 
 '{"theme": "modern", "features": ["whatsapp", "referrals", "analytics", "3d"], "branding": {"primary_color": "#0f172a", "secondary_color": "#f8fafc"}}', 'active');

-- Insert demo stores
INSERT INTO stores (id, tenant_id, name, description, slug, logo_url, banner_url, status, communication_config) VALUES
('660e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440001', 'Modern Furniture Gallery', 
 'Discover our curated collection of contemporary furniture and home decor through immersive virtual tours.', 
 'modern-furniture', 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=200', 
 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800', 'active',
 '{"whatsapp_enabled": true, "whatsapp_number": "+1234567890", "external_website_enabled": true, "external_website_url": "https://modernfurniture.example.com", "track_referrals": true}'),
 
('660e8400-e29b-41d4-a716-446655440002', '550e8400-e29b-41d4-a716-446655440001', 'Artisan Jewelry Studio', 
 'Handcrafted jewelry pieces showcased in stunning 360° virtual environments.', 
 'artisan-jewelry', 'https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?w=200', 
 'https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?w=800', 'active',
 '{"whatsapp_enabled": true, "whatsapp_number": "+1234567891", "external_website_enabled": true, "external_website_url": "https://artisanjewelry.example.com", "track_referrals": true}'),

('660e8400-e29b-41d4-a716-446655440003', '550e8400-e29b-41d4-a716-446655440002', 'Luxury Fashion Boutique', 
 'Premium fashion collections presented in an exclusive virtual showroom experience.', 
 'luxury-fashion', 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=200', 
 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=800', 'active',
 '{"whatsapp_enabled": true, "whatsapp_number": "+1234567892", "external_website_enabled": true, "external_website_url": "https://luxuryfashion.example.com", "track_referrals": true}'),

('660e8400-e29b-41d4-a716-446655440004', '550e8400-e29b-41d4-a716-446655440003', 'Tech Innovation Hub', 
 'Cutting-edge technology products showcased with interactive 3D demonstrations.', 
 'tech-hub', 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=200', 
 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=800', 'active',
 '{"whatsapp_enabled": true, "whatsapp_number": "+1234567893", "external_website_enabled": true, "external_website_url": "https://techhub.example.com", "track_referrals": true}');

-- Insert demo tours
INSERT INTO tours (id, tenant_id, store_id, title, description, tour_type, tour_url, hotspots, metadata, status) VALUES
('770e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440001', '660e8400-e29b-41d4-a716-446655440001', 
 'Modern Furniture Showroom Tour', 'Explore our contemporary furniture collection in this immersive 360° tour.', 
 'panorama', 'https://example.com/tours/modern-furniture-360', 
 '[{"id": "hotspot1", "position": {"x": 0.5, "y": 0.3}, "type": "product", "title": "Designer Sofa", "description": "Premium Italian leather sofa"}, {"id": "hotspot2", "position": {"x": 0.7, "y": 0.4}, "type": "info", "title": "Store Info", "description": "Contact us for more details"}]',
 '{"duration": "5-10 minutes", "featured": true, "difficulty": "easy"}', 'active'),

('770e8400-e29b-41d4-a716-446655440002', '550e8400-e29b-41d4-a716-446655440001', '660e8400-e29b-41d4-a716-446655440002', 
 'Jewelry Workshop Experience', 'Step inside our artisan workshop and see how each piece is crafted.', 
 'video', 'https://example.com/tours/jewelry-workshop-video', 
 '[{"id": "hotspot1", "position": {"x": 0.3, "y": 0.5}, "type": "product", "title": "Handcrafted Ring", "description": "Custom engagement rings"}, {"id": "hotspot2", "position": {"x": 0.8, "y": 0.2}, "type": "whatsapp", "title": "Contact Artisan", "description": "Speak directly with our master jeweler"}]',
 '{"duration": "3-5 minutes", "featured": true, "difficulty": "easy"}', 'active'),

('770e8400-e29b-41d4-a716-446655440003', '550e8400-e29b-41d4-a716-446655440002', '660e8400-e29b-41d4-a716-446655440003', 
 'Luxury Fashion Runway', 'Experience our latest collection in an exclusive virtual runway show.', 
 '3d', 'https://example.com/tours/luxury-fashion-3d', 
 '[{"id": "hotspot1", "position": {"x": 0.4, "y": 0.6}, "type": "product", "title": "Designer Dress", "description": "Limited edition evening wear"}, {"id": "hotspot2", "position": {"x": 0.6, "y": 0.3}, "type": "external", "title": "Shop Collection", "description": "View full collection online"}]',
 '{"duration": "7-12 minutes", "featured": true, "difficulty": "medium"}', 'active'),

('770e8400-e29b-41d4-a716-446655440004', '550e8400-e29b-41d4-a716-446655440003', '660e8400-e29b-41d4-a716-446655440004', 
 'Tech Innovation Lab', 'Interactive demonstration of our latest technology products and innovations.', 
 '3d', 'https://example.com/tours/tech-lab-3d', 
 '[{"id": "hotspot1", "position": {"x": 0.2, "y": 0.4}, "type": "product", "title": "Smart Device", "description": "Next-gen IoT solution"}, {"id": "hotspot2", "position": {"x": 0.7, "y": 0.5}, "type": "demo", "title": "Live Demo", "description": "See the product in action"}]',
 '{"duration": "10-15 minutes", "featured": true, "difficulty": "advanced"}', 'active');

-- Insert demo products
INSERT INTO products (id, store_id, tenant_id, name, description, price, currency, images, has_3d_model, vr_ready, video_360_url, external_url, whatsapp_inquiry_enabled, referral_tracking_enabled, metadata, status) VALUES
('880e8400-e29b-41d4-a716-446655440001', '660e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440001', 
 'Designer Italian Leather Sofa', 'Premium handcrafted sofa made from the finest Italian leather. Features ergonomic design and customizable configurations.', 
 2499.99, 'USD', 
 '["https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400", "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&q=80&auto=format&fit=crop&ixlib=rb-4.0.3"]',
 true, true, 'https://example.com/360/sofa-view', 'https://modernfurniture.example.com/products/italian-leather-sofa',
 true, true, '{"category": "furniture", "material": "leather", "color_options": ["black", "brown", "white"], "dimensions": {"width": 220, "depth": 95, "height": 85}}', 'active'),

('880e8400-e29b-41d4-a716-446655440002', '660e8400-e29b-41d4-a716-446655440002', '550e8400-e29b-41d4-a716-446655440001', 
 'Handcrafted Diamond Engagement Ring', 'Exquisite diamond ring crafted by master artisans. Each piece is unique and made to order.', 
 3999.99, 'USD', 
 '["https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?w=400", "https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?w=400&q=80&auto=format&fit=crop&ixlib=rb-4.0.3"]',
 true, false, null, 'https://artisanjewelry.example.com/products/diamond-engagement-ring',
 true, true, '{"category": "jewelry", "material": "gold", "stone": "diamond", "carat": 1.5, "certification": "GIA"}', 'active'),

('880e8400-e29b-41d4-a716-446655440003', '660e8400-e29b-41d4-a716-446655440003', '550e8400-e29b-41d4-a716-446655440002', 
 'Limited Edition Evening Gown', 'Exclusive designer evening gown from our luxury collection. Only 10 pieces available worldwide.', 
 5999.99, 'USD', 
 '["https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=400", "https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=400&q=80&auto=format&fit=crop&ixlib=rb-4.0.3"]',
 false, true, 'https://example.com/360/evening-gown', 'https://luxuryfashion.example.com/products/limited-evening-gown',
 true, true, '{"category": "fashion", "designer": "Maria Valentina", "size_range": ["XS", "S", "M", "L"], "limited_edition": true, "pieces_available": 10}', 'active'),

('880e8400-e29b-41d4-a716-446655440004', '660e8400-e29b-41d4-a716-446655440004', '550e8400-e29b-41d4-a716-446655440003', 
 'Smart Home IoT Hub', 'Next-generation smart home control system with AI integration and voice control capabilities.', 
 799.99, 'USD', 
 '["https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400", "https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&q=80&auto=format&fit=crop&ixlib=rb-4.0.3"]',
 true, true, 'https://example.com/360/smart-hub', 'https://techhub.example.com/products/smart-home-hub',
 true, true, '{"category": "technology", "connectivity": ["WiFi", "Bluetooth", "Zigbee"], "compatibility": ["Alexa", "Google", "Apple"], "warranty": "2 years"}', 'active');

-- Insert demo users
INSERT INTO users (id, tenant_id, email, name, role, status) VALUES
('990e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440001', '<EMAIL>', 'Demo Admin', 'tenant_admin', 'active'),
('990e8400-e29b-41d4-a716-446655440002', '550e8400-e29b-41d4-a716-446655440001', '<EMAIL>', 'Modern Furniture Manager', 'store_owner', 'active'),
('990e8400-e29b-41d4-a716-446655440003', '550e8400-e29b-41d4-a716-446655440002', '<EMAIL>', 'Luxury Admin', 'tenant_admin', 'active'),
('990e8400-e29b-41d4-a716-446655440004', '550e8400-e29b-41d4-a716-446655440003', '<EMAIL>', 'Tech Admin', 'tenant_admin', 'active');

-- Insert demo product inquiries
INSERT INTO product_inquiries (id, product_id, store_id, tenant_id, customer_name, customer_email, customer_phone, inquiry_type, message, status, whatsapp_message_id, referral_url) VALUES
('aa0e8400-e29b-41d4-a716-446655440001', '880e8400-e29b-41d4-a716-446655440001', '660e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440001', 
 'John Smith', '<EMAIL>', '+1234567890', 'whatsapp', 'I am interested in the Italian leather sofa. Can you provide more details about customization options?', 
 'sent', 'wa_msg_001', null),
 
('aa0e8400-e29b-41d4-a716-446655440002', '880e8400-e29b-41d4-a716-446655440002', '660e8400-e29b-41d4-a716-446655440002', '550e8400-e29b-41d4-a716-446655440001', 
 'Sarah Johnson', '<EMAIL>', '+1234567891', 'external_referral', 'Looking for a custom engagement ring. Saw your virtual tour and loved the craftsmanship!', 
 'responded', null, 'https://artisanjewelry.example.com/ref/sarah-j-001');

-- Insert demo referral analytics
INSERT INTO referral_analytics (id, product_id, store_id, tenant_id, referral_url, clicks, conversions, revenue_tracked, utm_source, utm_medium, utm_campaign) VALUES
('bb0e8400-e29b-41d4-a716-446655440001', '880e8400-e29b-41d4-a716-446655440001', '660e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440001', 
 'https://modernfurniture.example.com/products/italian-leather-sofa?utm_source=virtualrealtour&utm_medium=referral&utm_campaign=sofa_showcase', 
 156, 12, 29999.88, 'virtualrealtour', 'referral', 'sofa_showcase'),
 
('bb0e8400-e29b-41d4-a716-446655440002', '880e8400-e29b-41d4-a716-446655440002', '660e8400-e29b-41d4-a716-446655440002', '550e8400-e29b-41d4-a716-446655440001', 
 'https://artisanjewelry.example.com/products/diamond-engagement-ring?utm_source=virtualrealtour&utm_medium=referral&utm_campaign=jewelry_tour', 
 89, 7, 27999.93, 'virtualrealtour', 'referral', 'jewelry_tour'),
 
('bb0e8400-e29b-41d4-a716-446655440003', '880e8400-e29b-41d4-a716-446655440003', '660e8400-e29b-41d4-a716-446655440003', '550e8400-e29b-41d4-a716-446655440002', 
 'https://luxuryfashion.example.com/products/limited-evening-gown?utm_source=virtualrealtour&utm_medium=referral&utm_campaign=luxury_collection', 
 234, 3, 17999.97, 'virtualrealtour', 'referral', 'luxury_collection');
