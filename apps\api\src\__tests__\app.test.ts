import request from 'supertest';
import app from '../app';

describe('App', () => {
  describe('GET /', () => {
    it('should return API information', async () => {
      const response = await request(app)
        .get('/')
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        message: 'VirtualRealTour API Server',
        version: '1.0.0',
      });
    });
  });

  describe('GET /api/health', () => {
    it('should return health status', async () => {
      const response = await request(app)
        .get('/api/health')
        .set('Host', 'demo.virtualrealtour.com')
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        data: {
          status: 'healthy',
          version: '1.0.0',
        },
      });
    });
  });

  describe('GET /api', () => {
    it('should return API information', async () => {
      const response = await request(app)
        .get('/api')
        .set('Host', 'demo.virtualrealtour.com')
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        data: {
          name: 'VirtualRealTour API',
          version: '1.0.0',
        },
      });
    });
  });

  describe('404 handling', () => {
    it('should return 404 for unknown routes', async () => {
      const response = await request(app)
        .get('/unknown-route')
        .expect(404);

      expect(response.body).toMatchObject({
        success: false,
        error: expect.stringContaining('not found'),
      });
    });
  });
});
